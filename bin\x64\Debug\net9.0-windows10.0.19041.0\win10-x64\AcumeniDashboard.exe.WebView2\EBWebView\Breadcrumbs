0:06:40 Microsoft.Shutdown.TabStripModel.CloseWebContentses_FastShutdown
0:06:40 Microsoft.Shutdown.TabStripModel.ShouldRunUnloadListenerBeforeClosing_false
0:06:40 Microsoft.Shutdown.TabStripModel.RunUnloadListenerBeforeClosing_false
0:06:40 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosedAll
0:06:40 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
0:06:40 Microsoft.UnloadController.ClearUnloadState
0:06:40 Browser1 Close Tab1 at 0
0:06:40 Tab1 WebContentsDestroyed
0:06:40 Microsoft.UnloadController.TabStripEmpty
0:06:40 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:06:40 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:06:40 Microsoft.Shutdown.OnWindowClosing
0:06:40 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:06:40 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:06:40 Microsoft.Shutdown.OnWindowClosingPostClearBrowsingData_NoBrowsingDataCleared
0:06:40 Widget Closed: BrowserFrame
0:06:40 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:06:40 Microsoft.Shutdown.OnWindowClosing
0:06:40 Microsoft.Shutdown.OnWindowClosing_DeleteScheduled
0:06:40 Microsoft.Last_Browser_Removed
0:06:40 Microsoft.Shutdown.ShutdownIfNoBrowsers
0:06:40 Microsoft.Shutdown.SetTryingToQuit_Quitting
0:06:40 Microsoft.Shutdown.NotifyAppTerminating
0:06:40 Microsoft.Shutdown.OnAppExiting
0:06:40 Microsoft.Shutdown.HandleAppExitingForPlatform_Aura
0:06:40 Microsoft.Shutdown.HandleAppExitingForPlatform_Aura.NotificationUIManager_StartShutdown
0:06:40 Microsoft.Shutdown.HandleAppExitingForPlatform_Aura.CloseAllSecondaryWidgets
0:06:40 Microsoft.Shutdown.NotifyAppTerminating
0:06:40 Microsoft.Shutdown.OnAppExiting
0:06:41 Shutdown
0:00:00 Startup
0:00:00 Microsoft.DeleteProfileHelper.CleanUpEphemeralProfiles
0:00:00 Microsoft.DeleteProfileHelper.CleanUpDeletedProfiles
0:00:00 Microsoft.NewBrowser_Popup
0:00:00 Microsoft.BrowserList.AddBrowser
0:00:00 Browser1 Insert active Tab1 at 0
0:00:00 Tab1 StartNav1 #auto_toplevel
0:00:00 Tab1 FinishNav1
0:00:00 Tab1 PageLoad
0:00:01 Memory Pressure: Critical
0:00:01 Tab1 StartNav2 #typed
0:00:03 Tab1 FinishNav2
0:00:04 Memory Pressure: Critical
0:00:08 Tab1 PageLoad
0:00:09 Memory Pressure: Moderate
0:00:19 Memory Pressure: Moderate
0:00:25 Memory Pressure: Critical
0:00:30 Memory Pressure: Moderate
0:00:40 Memory Pressure: Critical
0:00:45 Memory Pressure: Critical
0:00:50 Memory Pressure: Moderate
0:01:00 Memory Pressure: Moderate
0:01:05 Memory Pressure: Critical
0:01:10 Memory Pressure: Critical
0:01:15 Memory Pressure: Critical
0:01:20 Memory Pressure: Critical
0:01:25 Memory Pressure: Critical
0:01:30 Memory Pressure: Critical
0:02:35 Memory Pressure: Moderate
0:02:45 Memory Pressure: Moderate
0:02:55 Memory Pressure: Moderate
0:03:05 Memory Pressure: Moderate
0:03:15 Memory Pressure: Moderate
0:03:25 Memory Pressure: Critical
0:03:30 Memory Pressure: Moderate
0:03:40 Memory Pressure: Moderate
0:03:50 Memory Pressure: Moderate
0:04:01 Memory Pressure: Moderate
0:04:11 Memory Pressure: Moderate
0:04:21 Memory Pressure: Moderate
0:04:32 Memory Pressure: Moderate
0:04:42 Memory Pressure: Moderate
0:04:52 Memory Pressure: Moderate
0:05:02 Memory Pressure: Moderate
0:05:12 Memory Pressure: Moderate
0:05:22 Memory Pressure: Moderate
0:05:32 Memory Pressure: Moderate
0:05:42 Memory Pressure: Moderate
