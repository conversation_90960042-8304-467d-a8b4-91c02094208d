<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <TargetFrameworks>net9.0-maccatalyst</TargetFrameworks>
    <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>

    <OutputType>Exe</OutputType>
    <RootNamespace>AcumeniDashboard</RootNamespace>
    <UseMaui>true</UseMaui>
    <SingleProject>true</SingleProject>
    <ImplicitUsings>enable</ImplicitUsings>
    <EnableDefaultCssItems>false</EnableDefaultCssItems>
    <Nullable>enable</Nullable>

    <!-- Display name -->
    <ApplicationTitle>Acumeni Dashboard</ApplicationTitle>

    <!-- App identifier -->
    <ApplicationId>com.acumeni.dashboard</ApplicationId>

    <!-- Versions -->
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <ApplicationVersion>1</ApplicationVersion>

    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">13.1</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
    <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
    <WindowsPackageType Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">None</WindowsPackageType>
    <RuntimeIdentifier Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">win10-x64</RuntimeIdentifier>
    <Platforms>AnyCPU;x64</Platforms>
    <DefaultLanguage>en</DefaultLanguage>
  </PropertyGroup>

  <!-- MAUI Deployment Configuration -->
  <PropertyGroup>
    <EnableDeployment>true</EnableDeployment>
    <DeployOnBuild>true</DeployOnBuild>
    <UseWinUI>true</UseWinUI>
  </PropertyGroup>

  <ItemGroup>
    <!-- App Icon -->
    <MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

    <!-- Splash Screen -->
    <MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

    <!-- Images -->
    <MauiImage Include="Resources\Images\*" />
    <MauiImage Update="Resources\Images\dotnet_bot.svg" BaseSize="168,208" />

    <!-- Custom Fonts -->
    <MauiFont Include="Resources\Fonts\*" />

    <!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
    <MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Maui.Controls" Version="$(MauiVersion)" />
    <PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="$(MauiVersion)" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebView.Maui" Version="$(MauiVersion)" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />

    <!-- Data Analysis -->
    <PackageReference Include="Microsoft.Data.Analysis" Version="0.21.1" />
    <PackageReference Include="MudBlazor" Version="8.9.0" />

    <!-- Sentiment Analysis -->
    <PackageReference Include="VaderSharp2" Version="3.3.2" />

    <!-- Logging -->
    <PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />

    <!-- Scheduling -->
    <PackageReference Include="Quartz.Extensions.Hosting" Version="3.8.0" />

    <!-- HTTP Client -->
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0" />

    <!-- Configuration -->
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.0" />

    <!-- OpenAI -->
    <PackageReference Include="OpenAI" Version="2.0.0" />

    <!-- JSON -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />

    <!-- Charts -->
    <PackageReference Include="Plotly.Blazor" Version="6.0.2" />

    <!-- Bootstrap -->

    <!-- Environment Variables -->
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="comment_themes.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="sentiment_scores.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <None Update="app.log">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="last_id.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="processed_data.csv">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="raw_data.csv">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
