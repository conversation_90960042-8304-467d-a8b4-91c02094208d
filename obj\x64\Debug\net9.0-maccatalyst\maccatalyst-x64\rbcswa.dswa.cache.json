{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["vjR4NOSa3nxno9wMcwiz6c8xMwy7qjHxBS/C83KxRow=", "bURnXS//Tglcsr2AgrIvJRXEVrawNvMT4WvGBSZFJVo=", "St/98gUSlpoRbxqoJ8B+Kr6QkE7mEryt3hP8MSLB5eg=", "B/yOjhZVTMR8o4jhoExLYZaqZd+F4A0n/GCxdzmBSK8=", "1iJHx+Kp1vQaC8v9sNGQYsy9c2UrOeehV9+01PTh0EY=", "RiQMznW3qcaPkHEmtW9tV3rQaUc8kbhnFKxFIdLsQKg="], "CachedAssets": {"vjR4NOSa3nxno9wMcwiz6c8xMwy7qjHxBS/C83KxRow=": {"Identity": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\xtrujjgkm4-aieg4310hh.gz", "SourceId": "Plotly<PERSON>", "SourceType": "Package", "ContentRoot": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "_content/Plotly.Blazor", "RelativePath": "plotly-interop.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\plotly.blazor\\4.1.0\\staticwebassets\\plotly-interop.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hd0nw6jzyh", "Integrity": "pUnL2/1ZBINTh623Tiwx6RYGonvvhEdlk6KgQRek+1g=", "CopyToOutputDirectory": "Always", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\plotly.blazor\\4.1.0\\staticwebassets\\plotly-interop.js", "FileLength": 953, "LastWriteTime": "2025-07-15T08:18:35.6771693+00:00"}, "bURnXS//Tglcsr2AgrIvJRXEVrawNvMT4WvGBSZFJVo=": {"Identity": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\mnb9s2pngy-hhex5g9j6i.gz", "SourceId": "Plotly<PERSON>", "SourceType": "Package", "ContentRoot": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "_content/Plotly.Blazor", "RelativePath": "plotly-latest.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\plotly.blazor\\4.1.0\\staticwebassets\\plotly-latest.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jojcie3vcz", "Integrity": "oyh6wUVsSoZqJUSJMPmkaKsT9bn0+yHo/4ZF9D3xSvg=", "CopyToOutputDirectory": "Always", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\plotly.blazor\\4.1.0\\staticwebassets\\plotly-latest.min.js", "FileLength": 1089015, "LastWriteTime": "2025-07-15T08:18:36.2828857+00:00"}, "1iJHx+Kp1vQaC8v9sNGQYsy9c2UrOeehV9+01PTh0EY=": {"Identity": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\vz72gntm4v-binpjx7utk.gz", "SourceId": "AcumeniDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint=binpjx7utk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\css\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s5poaa14z9", "Integrity": "YFfbIZf4TmlhMo3a8orCQH20ox12ZMXY/R5MbNoLxFc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\css\\app.css", "FileLength": 882, "LastWriteTime": "2025-07-15T08:18:35.679194+00:00"}, "RiQMznW3qcaPkHEmtW9tV3rQaUc8kbhnFKxFIdLsQKg=": {"Identity": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\laa0xb5mlh-z0kvmgt2ev.gz", "SourceId": "AcumeniDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint=z0kvmgt2ev}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uir425lx1n", "Integrity": "qpVkyAIMcFSxTh5Cpf5zIt85ppSJsCUc2TbFwmyYVYw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\index.html", "FileLength": 459, "LastWriteTime": "2025-07-16T11:51:42.034255+00:00"}, "B/yOjhZVTMR8o4jhoExLYZaqZd+F4A0n/GCxdzmBSK8=": {"Identity": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\0wz98yz2xy-b8x8f7e52z.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z2p08kpgbn", "Integrity": "BRlqmf2WzpTbcePWG15cPM4g6sa7ssVZeZ5ZBmZqjng=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "FileLength": 15402, "LastWriteTime": "2025-07-16T11:51:42.0619746+00:00"}, "St/98gUSlpoRbxqoJ8B+Kr6QkE7mEryt3hP8MSLB5eg=": {"Identity": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\tzxjg6is5z-sowobu9fea.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "59wrnbo615", "Integrity": "96Cl4EXJY5eN8ZZxPJLgMEvyRaW3jdF08SbOpeIwjjc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "FileLength": 65487, "LastWriteTime": "2025-07-16T11:51:42.0934655+00:00"}}, "CachedCopyCandidates": {}}