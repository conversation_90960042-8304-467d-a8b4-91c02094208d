{"format": 1, "restore": {"C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\AcumeniDashboard.csproj": {}}, "projects": {"C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\AcumeniDashboard.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\AcumeniDashboard.csproj", "projectName": "AcumeniDashboard", "projectPath": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\AcumeniDashboard.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-maccatalyst", "net9.0-windows10.0.19041.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-maccatalyst18.5": {"targetAlias": "net9.0-maccatalyst", "projectReferences": {}}, "net9.0-windows10.0.19041": {"targetAlias": "net9.0-windows10.0.19041.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-maccatalyst18.5": {"targetAlias": "net9.0-maccatalyst", "dependencies": {"DotNetEnv": {"target": "Package", "version": "[3.1.1, )"}, "Microsoft.AspNetCore.Components.WebView.Maui": {"target": "Package", "version": "[9.0.51, )"}, "Microsoft.Data.Analysis": {"target": "Package", "version": "[0.21.1, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.51, )"}, "Microsoft.Maui.Controls.Compatibility": {"target": "Package", "version": "[9.0.51, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.6, )", "autoReferenced": true}, "MudBlazor": {"target": "Package", "version": "[8.9.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "OpenAI": {"target": "Package", "version": "[2.0.0, )"}, "Plotly.Blazor": {"target": "Package", "version": "[6.0.2, )"}, "Quartz.Extensions.Hosting": {"target": "Package", "version": "[3.8.0, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "VaderSharp2": {"target": "Package", "version": "[3.3.2, )"}}, "imports": ["xamarinios10", "net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.MacCatalyst": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0-windows10.0.19041": {"targetAlias": "net9.0-windows10.0.19041.0", "dependencies": {"DotNetEnv": {"target": "Package", "version": "[3.1.1, )"}, "Microsoft.AspNetCore.Components.WebView.Maui": {"target": "Package", "version": "[9.0.51, )"}, "Microsoft.Data.Analysis": {"target": "Package", "version": "[0.21.1, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.51, )"}, "Microsoft.Maui.Controls.Compatibility": {"target": "Package", "version": "[9.0.51, )"}, "MudBlazor": {"target": "Package", "version": "[8.9.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "OpenAI": {"target": "Package", "version": "[2.0.0, )"}, "Plotly.Blazor": {"target": "Package", "version": "[6.0.2, )"}, "Quartz.Extensions.Hosting": {"target": "Package", "version": "[3.8.0, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "VaderSharp2": {"target": "Package", "version": "[3.3.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.19041.57, 10.0.19041.57]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref.Windows": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}, "runtimes": {"maccatalyst-x64": {"#import": []}, "win10-x64": {"#import": []}}}}}