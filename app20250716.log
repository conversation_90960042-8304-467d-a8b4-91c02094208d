[2025-07-16 05:54:59.966 +01:00 INF] : Application starting up - Serilog configured successfully
[2025-07-16 05:55:04.990 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Loading environment variables...
[2025-07-16 05:55:05.079 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: .env file loaded successfully
[2025-07-16 05:55:05.094 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Using default themes for industry: Healthcare
[2025-07-16 05:55:05.095 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Active Industry: Healthcare
[2025-07-16 05:55:05.096 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Title: Healthcare Voice of Customer Dashboard
[2025-07-16 05:55:05.096 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Subtitle: Real-time analysis of healthcare survey responses
[2025-07-16 05:55:05.097 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Survey Code: 81844
[2025-07-16 05:55:05.097 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: CSAT Question: Validated_Q1
[2025-07-16 05:55:05.097 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: NPS Question: Validated_Q2
[2025-07-16 05:55:05.097 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Verbatim Question: Validated_Q3
[2025-07-16 05:55:05.098 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Phone Column: Phone_Number
[2025-07-16 05:55:05.274 +01:00 INF] AcumeniDashboard.Services.SentimentAnalysisService: Loaded 1000 sentiment scores from file
[2025-07-16 05:55:05.284 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Loaded 1000 comment themes from file
[2025-07-16 05:55:05.285 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Using default themes for industry: Healthcare
[2025-07-16 05:55:05.289 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Initialized 6 theme categories
[2025-07-16 05:55:05.396 +01:00 INF] AcumeniDashboard.Services.AcumeniApiService: Loaded 1000 records from processed data file
[2025-07-16 05:55:05.397 +01:00 INF] AcumeniDashboard.Services.DataAnalyzerService: Columns in loaded data: SessionId, Date, Csat, Nps, Comments, Contact
[2025-07-16 05:55:05.397 +01:00 INF] AcumeniDashboard.Services.DataAnalyzerService: Number of records with non-empty comments: 1000
[2025-07-16 05:55:05.437 +01:00 INF] AcumeniDashboard.Services.DataAnalyzerService: Limited comments to the top 100
[2025-07-16 05:55:05.437 +01:00 INF] AcumeniDashboard.Services.DataAnalyzerService: Returning 100 processed comments
[2025-07-16 06:29:37.181 +01:00 INF] : Application starting up - Serilog configured successfully
[2025-07-16 06:29:40.773 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Loading environment variables...
[2025-07-16 06:29:40.828 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: .env file loaded successfully
[2025-07-16 06:29:40.835 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Using default themes for industry: Healthcare
[2025-07-16 06:29:40.836 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Active Industry: Healthcare
[2025-07-16 06:29:40.837 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Title: Healthcare Voice of Customer Dashboard
[2025-07-16 06:29:40.837 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Subtitle: Real-time analysis of healthcare survey responses
[2025-07-16 06:29:40.837 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Survey Code: 81844
[2025-07-16 06:29:40.837 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: CSAT Question: Validated_Q1
[2025-07-16 06:29:40.837 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: NPS Question: Validated_Q2
[2025-07-16 06:29:40.837 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Verbatim Question: Validated_Q3
[2025-07-16 06:29:40.838 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Phone Column: Phone_Number
[2025-07-16 06:29:41.020 +01:00 INF] AcumeniDashboard.Services.SentimentAnalysisService: Loaded 1000 sentiment scores from file
[2025-07-16 06:29:41.030 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Loaded 1000 comment themes from file
[2025-07-16 06:29:41.031 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Using default themes for industry: Healthcare
[2025-07-16 06:29:41.034 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Initialized 6 theme categories
[2025-07-16 06:29:41.136 +01:00 INF] AcumeniDashboard.Services.AcumeniApiService: Loaded 1000 records from processed data file
[2025-07-16 06:29:41.137 +01:00 INF] AcumeniDashboard.Services.DataAnalyzerService: Columns in loaded data: SessionId, Date, Csat, Nps, Comments, Contact
[2025-07-16 06:29:41.137 +01:00 INF] AcumeniDashboard.Services.DataAnalyzerService: Number of records with non-empty comments: 1000
[2025-07-16 06:29:41.173 +01:00 INF] AcumeniDashboard.Services.DataAnalyzerService: Limited comments to the top 100
[2025-07-16 06:29:41.173 +01:00 INF] AcumeniDashboard.Services.DataAnalyzerService: Returning 100 processed comments
[2025-07-16 06:36:49.752 +01:00 INF] : Application starting up - Serilog configured successfully
[2025-07-16 06:36:57.177 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Loading environment variables...
[2025-07-16 06:36:57.289 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: .env file loaded successfully
[2025-07-16 06:36:57.306 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Using default themes for industry: Healthcare
[2025-07-16 06:36:57.307 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Active Industry: Healthcare
[2025-07-16 06:36:57.308 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Title: Healthcare Voice of Customer Dashboard
[2025-07-16 06:36:57.308 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Subtitle: Real-time analysis of healthcare survey responses
[2025-07-16 06:36:57.308 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Survey Code: 81844
[2025-07-16 06:36:57.308 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: CSAT Question: Validated_Q1
[2025-07-16 06:36:57.308 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: NPS Question: Validated_Q2
[2025-07-16 06:36:57.308 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Verbatim Question: Validated_Q3
[2025-07-16 06:36:57.308 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Phone Column: Phone_Number
[2025-07-16 06:36:57.622 +01:00 INF] AcumeniDashboard.Services.SentimentAnalysisService: Loaded 1000 sentiment scores from file
[2025-07-16 06:36:57.643 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Loaded 1000 comment themes from file
[2025-07-16 06:36:57.644 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Using default themes for industry: Healthcare
[2025-07-16 06:36:57.650 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Initialized 6 theme categories
[2025-07-16 06:36:57.857 +01:00 INF] AcumeniDashboard.Services.AcumeniApiService: Loaded 1000 records from processed data file
[2025-07-16 06:36:57.858 +01:00 INF] AcumeniDashboard.Services.DataAnalyzerService: Columns in loaded data: SessionId, Date, Csat, Nps, Comments, Contact
[2025-07-16 06:36:57.860 +01:00 INF] AcumeniDashboard.Services.DataAnalyzerService: Number of records with non-empty comments: 1000
[2025-07-16 06:36:57.939 +01:00 INF] AcumeniDashboard.Services.DataAnalyzerService: Limited comments to the top 100
[2025-07-16 06:36:57.939 +01:00 INF] AcumeniDashboard.Services.DataAnalyzerService: Returning 100 processed comments
[2025-07-16 07:30:46.475 +01:00 INF] : Application starting up - Serilog configured successfully
[2025-07-16 07:30:56.240 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Loading environment variables...
[2025-07-16 07:30:56.510 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: .env file loaded successfully
[2025-07-16 07:30:56.517 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Using default themes for industry: Healthcare
[2025-07-16 07:30:56.518 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Active Industry: Healthcare
[2025-07-16 07:30:56.519 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Title: Healthcare Voice of Customer Dashboard
[2025-07-16 07:30:56.519 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Subtitle: Real-time analysis of healthcare survey responses
[2025-07-16 07:30:56.519 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Survey Code: 81844
[2025-07-16 07:30:56.519 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: CSAT Question: Validated_Q1
[2025-07-16 07:30:56.519 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: NPS Question: Validated_Q2
[2025-07-16 07:30:56.519 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Verbatim Question: Validated_Q3
[2025-07-16 07:30:56.519 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Phone Column: Phone_Number
[2025-07-16 07:30:56.922 +01:00 INF] AcumeniDashboard.Services.SentimentAnalysisService: Loaded 1000 sentiment scores from file
[2025-07-16 07:30:56.943 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Loaded 1000 comment themes from file
[2025-07-16 07:30:56.944 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Using default themes for industry: Healthcare
[2025-07-16 07:30:56.948 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Initialized 6 theme categories
[2025-07-16 07:30:57.029 +01:00 INF] AcumeniDashboard.Services.AcumeniApiService: Loaded 1000 records from processed data file
[2025-07-16 07:30:57.029 +01:00 INF] AcumeniDashboard.Services.DataAnalyzerService: Columns in loaded data: SessionId, Date, Csat, Nps, Comments, Contact
[2025-07-16 07:30:57.030 +01:00 INF] AcumeniDashboard.Services.DataAnalyzerService: Number of records with non-empty comments: 1000
[2025-07-16 07:30:57.065 +01:00 INF] AcumeniDashboard.Services.DataAnalyzerService: Limited comments to the top 100
[2025-07-16 07:30:57.065 +01:00 INF] AcumeniDashboard.Services.DataAnalyzerService: Returning 100 processed comments
[2025-07-16 07:49:20.025 +01:00 INF] : Application starting up - Serilog configured successfully
[2025-07-16 07:49:25.248 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Loading environment variables...
[2025-07-16 07:49:25.320 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: .env file loaded successfully
[2025-07-16 07:49:25.330 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Using default themes for industry: Healthcare
[2025-07-16 07:49:25.332 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Active Industry: Healthcare
[2025-07-16 07:49:25.332 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Title: Healthcare Voice of Customer Dashboard
[2025-07-16 07:49:25.332 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Subtitle: Real-time analysis of healthcare survey responses
[2025-07-16 07:49:25.332 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Survey Code: 81844
[2025-07-16 07:49:25.333 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: CSAT Question: Validated_Q1
[2025-07-16 07:49:25.333 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: NPS Question: Validated_Q2
[2025-07-16 07:49:25.333 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Verbatim Question: Validated_Q3
[2025-07-16 07:49:25.333 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Phone Column: Phone_Number
[2025-07-16 07:49:25.612 +01:00 INF] AcumeniDashboard.Services.SentimentAnalysisService: Loaded 1000 sentiment scores from file
[2025-07-16 07:49:25.629 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Loaded 1000 comment themes from file
[2025-07-16 07:49:25.630 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Using default themes for industry: Healthcare
[2025-07-16 07:49:25.635 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Initialized 6 theme categories
[2025-07-16 08:05:16.833 +01:00 INF] : Application starting up - Serilog configured successfully
[2025-07-16 08:05:22.139 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Loading environment variables...
[2025-07-16 08:05:22.264 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: .env file loaded successfully
[2025-07-16 08:05:22.282 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Using default themes for industry: Healthcare
[2025-07-16 08:05:22.284 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Active Industry: Healthcare
[2025-07-16 08:05:22.284 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Title: Healthcare Voice of Customer Dashboard
[2025-07-16 08:05:22.285 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Subtitle: Real-time analysis of healthcare survey responses
[2025-07-16 08:05:22.285 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Survey Code: 81844
[2025-07-16 08:05:22.285 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: CSAT Question: Validated_Q1
[2025-07-16 08:05:22.285 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: NPS Question: Validated_Q2
[2025-07-16 08:05:22.285 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Verbatim Question: Validated_Q3
[2025-07-16 08:05:22.285 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Phone Column: Phone_Number
[2025-07-16 08:05:22.567 +01:00 INF] AcumeniDashboard.Services.SentimentAnalysisService: Loaded 1000 sentiment scores from file
[2025-07-16 08:05:22.580 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Loaded 1000 comment themes from file
[2025-07-16 08:05:22.581 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Using default themes for industry: Healthcare
[2025-07-16 08:05:22.587 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Initialized 6 theme categories
[2025-07-16 11:10:38.487 +01:00 INF] : Application starting up - Serilog configured successfully
[2025-07-16 11:10:46.435 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Loading environment variables...
[2025-07-16 11:10:46.605 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: .env file loaded successfully
[2025-07-16 11:10:46.641 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Using default themes for industry: Healthcare
[2025-07-16 11:10:46.644 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Active Industry: Healthcare
[2025-07-16 11:10:46.645 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Title: Healthcare Voice of Customer Dashboard
[2025-07-16 11:10:46.645 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Subtitle: Real-time analysis of healthcare survey responses
[2025-07-16 11:10:46.645 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Survey Code: 81844
[2025-07-16 11:10:46.646 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: CSAT Question: Validated_Q1
[2025-07-16 11:10:46.646 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: NPS Question: Validated_Q2
[2025-07-16 11:10:46.646 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Verbatim Question: Validated_Q3
[2025-07-16 11:10:46.646 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Phone Column: Phone_Number
[2025-07-16 11:10:47.091 +01:00 INF] AcumeniDashboard.Services.SentimentAnalysisService: Loaded 1000 sentiment scores from file
[2025-07-16 11:10:47.119 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Loaded 1000 comment themes from file
[2025-07-16 11:10:47.121 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Using default themes for industry: Healthcare
[2025-07-16 11:10:47.132 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Initialized 6 theme categories
[2025-07-16 11:12:31.048 +01:00 INF] : Application starting up - Serilog configured successfully
[2025-07-16 11:12:36.794 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Loading environment variables...
[2025-07-16 11:12:36.875 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: .env file loaded successfully
[2025-07-16 11:12:36.889 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Using default themes for industry: Healthcare
[2025-07-16 11:12:36.890 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Active Industry: Healthcare
[2025-07-16 11:12:36.891 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Title: Healthcare Voice of Customer Dashboard
[2025-07-16 11:12:36.891 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Subtitle: Real-time analysis of healthcare survey responses
[2025-07-16 11:12:36.891 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Survey Code: 81844
[2025-07-16 11:12:36.891 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: CSAT Question: Validated_Q1
[2025-07-16 11:12:36.891 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: NPS Question: Validated_Q2
[2025-07-16 11:12:36.891 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Verbatim Question: Validated_Q3
[2025-07-16 11:12:36.891 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Phone Column: Phone_Number
[2025-07-16 11:12:37.157 +01:00 INF] AcumeniDashboard.Services.SentimentAnalysisService: Loaded 1000 sentiment scores from file
[2025-07-16 11:12:37.169 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Loaded 1000 comment themes from file
[2025-07-16 11:12:37.170 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Using default themes for industry: Healthcare
[2025-07-16 11:12:37.177 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Initialized 6 theme categories
[2025-07-16 11:20:19.773 +01:00 INF] : Application starting up - Serilog configured successfully
[2025-07-16 11:20:28.733 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Loading environment variables...
[2025-07-16 11:20:28.796 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: .env file loaded successfully
[2025-07-16 11:20:28.807 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Using default themes for industry: Healthcare
[2025-07-16 11:20:28.809 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Active Industry: Healthcare
[2025-07-16 11:20:28.809 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Title: Healthcare Voice of Customer Dashboard
[2025-07-16 11:20:28.809 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Dashboard Subtitle: Real-time analysis of healthcare survey responses
[2025-07-16 11:20:28.809 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Survey Code: 81844
[2025-07-16 11:20:28.809 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: CSAT Question: Validated_Q1
[2025-07-16 11:20:28.810 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: NPS Question: Validated_Q2
[2025-07-16 11:20:28.810 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Verbatim Question: Validated_Q3
[2025-07-16 11:20:28.810 +01:00 INF] AcumeniDashboard.Services.ConfigurationService: Phone Column: Phone_Number
[2025-07-16 11:20:29.009 +01:00 INF] AcumeniDashboard.Services.SentimentAnalysisService: Loaded 1000 sentiment scores from file
[2025-07-16 11:20:29.024 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Loaded 1000 comment themes from file
[2025-07-16 11:20:29.025 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Using default themes for industry: Healthcare
[2025-07-16 11:20:29.030 +01:00 INF] AcumeniDashboard.Services.ThemeAnalysisService: Initialized 6 theme categories
