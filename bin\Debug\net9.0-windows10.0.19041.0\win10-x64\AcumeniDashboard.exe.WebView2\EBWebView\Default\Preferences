{"aadc_info": {"age_group": 0}, "accessibility": {"captions": {"headless_caption_enabled": false}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"app_window_placement": {"EdgeDevToolsApp": {"always_on_top": false, "bottom": 773, "left": 100, "maximized": false, "right": 1428, "top": 57, "work_area_bottom": 864, "work_area_left": 0, "work_area_right": 1536, "work_area_top": 0}}, "available_dark_theme_options": "All", "has_seen_welcome_page": false, "recent_theme_color_list": [**********.0, **********.0, **********.0, **********.0, **********.0], "user_level_features_context": {}}, "browser_content_container_height": 747, "browser_content_container_width": 1401, "browser_content_container_x": 0, "browser_content_container_y": 0, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 18242, "credentials_enable_service": false, "devtools": {"last_open_timestamp": "13397058895490", "preferences": {"EdgeDevToolsLayoutInfo": {"current_dock_state": 0, "horizontal_size": 300, "showEmulationMode": false, "vertical_size": 555}, "closeable-tabs": "{\"security\":true,\"heap-profiler\":true,\"resources\":true,\"lighthouse\":true,\"welcome\":true,\"timeline\":true,\"network\":true,\"cssoverview\":true,\"issues-pane\":true}", "cloud-release-notes": "{\"edgeVersion\":138,\"shouldOpenWelcome\":false,\"help\":[{\"title\":\"DevTools documentation\",\"linkId\":\"2196640\",\"localizedAnnouncementKey\":\"helpCard1\",\"iconName\":\"edge-documentation_book_filled\"},{\"title\":\"Overview of all tools\",\"linkId\":\"2196549\",\"localizedAnnouncementKey\":\"helpCard2\",\"iconName\":\"edge-developer-resources\"},{\"title\":\"Use Copilot to explain Console errors\",\"linkId\":\"2257416\",\"localizedAnnouncementKey\":\"helpCard3\",\"iconName\":\"edge-copilot\"},{\"title\":\"Videos about web development with Microsoft Edge\",\"linkId\":\"2196701\",\"localizedAnnouncementKey\":\"helpCard5\",\"iconName\":\"edge-run_command\"},{\"title\":\"Accessibility testing features\",\"linkId\":\"2196801\",\"localizedAnnouncementKey\":\"helpCard6\",\"iconName\":\"edge-documentation_book_filled\"},{\"title\":\"Use the Console tool to track down problems\",\"linkId\":\"2196702\",\"localizedAnnouncementKey\":\"helpCard7\",\"iconName\":\"edge-console\"},{\"title\":\"Modify and debug JS with the Sources tool\",\"linkId\":\"2196900\",\"localizedAnnouncementKey\":\"helpCard8\",\"iconName\":\"edge-sources\"},{\"title\":\"Find source files for a page using the search tool\",\"linkId\":\"2196802\",\"localizedAnnouncementKey\":\"helpCard9\",\"iconName\":\"edge-sources-search-sources-tab\"},{\"title\":\"Microsoft Edge DevTools for Visual Studio Code\",\"linkId\":\"2196901\",\"localizedAnnouncementKey\":\"helpCard10\",\"iconName\":\"edge-help_tooltips\"}],\"releaseNotes\":[{\"title\":\"Sync your DevTools settings between devices\",\"subtitle\":\"Enable settings sync lets you sync your DevTools settings across devices.\",\"linkId\":\"2324701\",\"localizedAnnouncementKey\":\"edgeAnnouncement1\"}],\"header\":{\"localizedKey\":\"highlightsFromTheLatestMicrosoft\",\"title\":\"What's New\"},\"learnHeader\":{\"localizedKey\":\"learnHeader\",\"title\":\"Learn\"},\"allAnnouncementsLinkText\":{\"localizedKey\":\"allAnnouncementsLinkText\",\"title\":\"View all\"},\"whatsNewVideo\":{\"title\":\"What's New in DevTools 115 - 125\",\"subtitle\":\"Check out our video series on the latest and greatest features in DevTools!\",\"linkId\":\"26zDq9Xhz7k\",\"imageName\":\"whats-new-115-125-thumbnail.jpg\",\"imageAltText\":\"A title card for the Microsoft Edge: What's New in DevTools 115 - 125 video\",\"localizedKey\":\"whatsNewVideo\"},\"viewAllLinkId\":\"2324502\",\"localized\":{\"en-US\":{\"panels/edge_welcome/ReleaseNotes.ts | helpCard1\":{\"message\":\"DevTools documentation\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard2\":{\"message\":\"Overview of all tools\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard3\":{\"message\":\"Use Copilot to explain Console errors\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard5\":{\"message\":\"Videos about web development with Microsoft Edge\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard6\":{\"message\":\"Accessibility testing features\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard7\":{\"message\":\"Use the Console tool to track down problems\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard8\":{\"message\":\"Modify and debug JS with the Sources tool\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard9\":{\"message\":\"Find source files for a page using the search tool\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard10\":{\"message\":\"Microsoft Edge DevTools for Visual Studio Code\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | edgeAnnouncement1\":{\"message\":\"Sync your DevTools settings between devices\",\"description\":\"Title of a release note, shown next to a description, in a list of release notes.\"},\"panels/edge_welcome/ReleaseNotes.ts | edgeAnnouncement1Description\":{\"message\":\"Enable settings sync lets you sync your DevTools settings across devices.\",\"description\":\"Description of a release note providing further details, shown next to each release note title.\"},\"panels/edge_welcome/ReleaseNotes.ts | learnHeader\":{\"message\":\"Learn\",\"description\":\"Title text of a header bar in the welcome tool.\"},\"panels/edge_welcome/ReleaseNotes.ts | allAnnouncementsLinkText\":{\"message\":\"View all\",\"description\":\"Title text of a header bar in the welcome tool.\"},\"panels/edge_welcome/ReleaseNotes.ts | highlightsFromTheLatestMicrosoft\":{\"message\":\"What's New\",\"description\":\"Title text of a header bar in the welcome tool.\"},\"panels/edge_welcome/ReleaseNotes.ts | whatsNewVideo\":{\"message\":\"What's New in DevTools 115 - 125\",\"description\":\"Title of a video summarizing the latest release, shown next to a description, above a list of release notes.\"},\"panels/edge_welcome/ReleaseNotes.ts | whatsNewVideoDescription\":{\"message\":\"Check out our video series on the latest and greatest features in DevTools!\",\"description\":\"Description of a video link providing further details\"}}}}", "console.sidebar-selected-filter": "\"message\"", "console.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "drawer-minimize-state": "false", "edge-inspector.actions-tab-split-view-state": "{\"vertical\":{\"size\":0,\"showMode\":\"Both\"},\"horizontal\":{\"size\":30,\"showMode\":\"Both\"}}", "inspector.drawer-split-view-state": "{\"horizontal\":{\"size\":0,\"showMode\":\"Both\"}}", "inspectorVersion": "38", "last-open-view-in-drawer": "\"console\"", "panel-selected-tab": "\"console\"", "panel-tab-order": "{\"welcome\":10,\"elements\":20,\"sources\":30,\"network\":40,\"timeline\":50,\"heap-profiler\":60,\"resources\":70,\"security\":80,\"lighthouse\":90,\"cssoverview\":100,\"console\":110}", "release-note-version-seen": "138", "sources-panel-navigator-split-view-state": "{\"vertical\":{\"size\":0,\"showMode\":\"Both\"}}", "sources-panel-split-view-state": "{\"vertical\":{\"size\":0,\"showMode\":\"Both\"},\"horizontal\":{\"size\":0,\"showMode\":\"Both\"}}", "tools-used": "{\"welcome\":1752580619234,\"console-view\":1752585295990,\"sources\":1752580619381,\"console\":1752585296128}", "undefined-tab-order": "{\"sources.scope-chain\":10,\"sources.watch\":20}"}, "synced_preferences_sync_disabled": {"syncedInspectorVersion": "38"}}, "domain_diversity": {"last_reporting_timestamp": "13397115303002650"}, "edge": {"bookmarks": {"last_dup_info_record_time": "13397134253731677"}, "msa_sso_info": {"allow_for_non_msa_profile": true}, "profile_sso_info": {"is_msa_first_profile": true, "msa_sso_algo_state": 1}, "services": {"signin_scoped_device_id": "78407ce0-bee8-4561-8472-0a8e52691750"}}, "edge_rewards": {"cache_data": "CAA=", "coachmark_promotions": {}, "hva_promotions": [], "refresh_status_muted_until": "13397645787302510"}, "edge_ux_config": {"assignmentcontext": "", "dataversion": "0", "experimentvariables": {}, "flights": {}}, "edge_wallet": {"passwords": {"password_lost_report_date": "13397134273893195"}}, "enterprise_profile_guid": "20e31ab6-3d17-4104-a724-9f3079001734", "extension": {"installed_extension_count": 2}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "commands": {}, "last_chrome_version": "138.0.3351.83", "pdf_upsell_triggered": false, "pinned_extension_migration": true, "pinned_extensions": []}, "fsd": {"retention_policy_last_version": 138}, "intl": {"selected_languages": "en-GB,en,en-US"}, "language_dwell_time_average": {"en": 340.88}, "language_usage_count": {"en": 25}, "media": {"engagement": {"schema_version": 5}}, "muid": {"last_sync": "13397134243719733", "values_seen": []}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "personalization_data_consent": {"personalization_in_context_consent_can_prompt": true, "personalization_in_context_count": 0}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 20, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "clear_browsing_data_cookies_exceptions": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://*******,*": {"last_modified": "13397142248606497", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "edge_ad_targeting": {}, "edge_ad_targeting_data": {}, "edge_sdsm": {}, "edge_split_screen": {}, "edge_u2f_api_request": {}, "edge_user_agent_token": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://*******:443,*": {"expiration": "13404918282632624", "last_modified": "13397142282632635", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 25}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "secure_network": {}, "secure_network_sites": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"https://*******:443,*": {"last_modified": "13397142276494424", "setting": {"lastEngagementTime": 1.3397142276494396e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 29.612535000000022}}}, "sleeping_tabs": {}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "tech_scam_detection": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "trackers_data": {"https://msedgedevtools.microsoft.com:443,*": {"last_modified": "13397054219775782", "setting": {"count": 1}}}, "tracking_org_exceptions": {}, "tracking_org_relationships": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "138.0.3351.83", "creation_time": "13397040987246846", "edge_password_is_using_new_login_db_path": false, "edge_password_login_db_path_flip_flop_count": 0, "edge_profile_id": "d51db337-b8d3-4c1d-adaa-b5dc8b27ddef", "exit_type": "Normal", "has_seen_signin_fre": false, "is_relative_to_aad": false, "last_engagement_time": "13397142276494396", "last_time_obsolete_http_credentials_removed": 1752567447.461467, "last_time_password_store_metrics_reported": 1752660673.892458, "managed_user_id": "", "name": "Profile 1", "network_pbs": {}, "observed_session_time": {"feedback_rating_in_product_help_observed_session_time_key_138.0.3351.83": 1960.0}, "password_hash_data_list": [], "signin_fre_seen_time": "13397040987287725", "were_old_google_logins_removed": true}, "reset_prepopulated_engines": false, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "sessions": {"event_log": [{"crashed": false, "time": "13397122162883951", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13397122413728708", "type": 2, "window_count": 0}, {"crashed": false, "time": "13397123119218657", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13397123669454652", "type": 2, "window_count": 0}, {"crashed": false, "time": "13397134243675000", "type": 0}, {"crashed": false, "time": "13397134353407196", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13397134731944410", "type": 2, "window_count": 0}, {"crashed": false, "time": "13397134824926944", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13397138700760541", "type": 2, "window_count": 0}, {"crashed": false, "time": "13397139033332810", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13397139449919222", "type": 2, "window_count": 0}, {"crashed": false, "time": "13397139534702513", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13397139565683462", "type": 2, "window_count": 0}, {"crashed": false, "time": "13397141208748306", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13397141862807692", "type": 2, "window_count": 0}, {"crashed": false, "time": "13397141927570385", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "*****************", "type": 2, "window_count": 0}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "*****************", "type": 2, "window_count": 0}], "session_data_status": 3}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-GB"]}, "sync": {"passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "user_experience_metrics": {"personalization_data_consent_enabled_last_known_value": false}}