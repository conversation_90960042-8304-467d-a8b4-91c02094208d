using Microsoft.AspNetCore.Components.WebView.Maui;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Events;
using AcumeniDashboard.Services;
using AcumeniDashboard.Models;
using Quartz;
using MudBlazor.Services;
using Plotly.Blazor;

namespace AcumeniDashboard;

public static class MauiProgram
{
	public static MauiApp CreateMauiApp()
	{
		// Configure Serilog with file-based logging to app.log only
		Log.Logger = new LoggerConfiguration()
			.MinimumLevel.Information()
			.MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
			.MinimumLevel.Override("System", LogEventLevel.Warning)
			.Enrich.FromLogContext()
			.WriteTo.File("app.log",
				shared: true,
				flushToDiskInterval: TimeSpan.FromSeconds(1),
				rollingInterval: RollingInterval.Day,
				retainedFileCountLimit: 7,
				outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}")
			.CreateLogger();

		// Test logging immediately
		Log.Information("Application starting up - Serilog configured successfully");

		var builder = MauiApp.CreateBuilder();
		builder
			.UseMauiApp<App>()
			.ConfigureFonts(fonts =>
			{
				fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
			});

		builder.Services.AddMauiBlazorWebView();

		// Add MudBlazor services
		builder.Services.AddMudServices();

		// Add Plotly.Blazor services
		builder.Services.AddPlotlyBlazor();

		// Configure logging to use Serilog
		builder.Logging.ClearProviders();
		builder.Logging.AddSerilog(Log.Logger);

		// Register HttpClient
		builder.Services.AddHttpClient();

		// Register environment configuration using the LoadFromEnvironment factory method
		builder.Services.AddSingleton<EnvironmentConfig>(provider => EnvironmentConfig.LoadFromEnvironment());

		// Register configuration services
		builder.Services.AddSingleton<ConfigurationService>();
		builder.Services.AddSingleton<ThemeAnalysisService>();

		// Register new line-by-line converted services
		builder.Services.AddSingleton<ISentimentAnalysisService, SentimentAnalysisService>();
		builder.Services.AddSingleton<IAcumeniApiService, AcumeniApiService>();
		builder.Services.AddSingleton<IDataAnalyzerService, DataAnalyzerService>();

		// Register legacy services for backward compatibility (if needed)
		builder.Services.AddSingleton<ICommentFilteringService, CommentFilteringService>();

		// Note: DataRefreshService is replaced by Quartz.NET DataRefreshJob

		// Add Quartz.NET services for scheduling (Python APScheduler equivalent)
		builder.Services.AddQuartz(q =>
		{
			// Python equivalent: scheduler.add_job(api.get_survey_data, 'interval', minutes=DATA_REFRESH_MINUTES)
			// Create job key
			var jobKey = new JobKey("DataRefreshJob");

			// Add the job
			q.AddJob<DataRefreshJob>(opts => opts.WithIdentity(jobKey));

			// Add trigger - runs every DATA_REFRESH_MINUTES (default 5 minutes)
			q.AddTrigger(opts => opts
				.ForJob(jobKey)
				.WithIdentity("DataRefreshTrigger")
				.WithSimpleSchedule(x => x
					.WithIntervalInMinutes(5) // Will be configurable via EnvironmentConfig
					.RepeatForever()));
		});

		// Add Quartz hosted service
		builder.Services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);

		// Configure Serilog logging
		builder.Logging.ClearProviders();
		builder.Logging.AddSerilog(Log.Logger);

#if DEBUG
		builder.Services.AddBlazorWebViewDeveloperTools();
		// Also add console logging in debug mode
		builder.Logging.AddDebug();
#endif

		var app = builder.Build();

		// Ensure proper cleanup of Serilog
		AppDomain.CurrentDomain.ProcessExit += (s, e) => Log.CloseAndFlush();

		return app;
	}
}
