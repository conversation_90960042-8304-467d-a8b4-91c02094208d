{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "_content/MudBlazor/MudBlazor.min.b8x8f7e52z.js", "AssetFile": "_content/MudBlazor/MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "73366"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Jul 2025 21:24:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b8x8f7e52z"}, {"Name": "integrity", "Value": "sha256-O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.js"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "_content/MudBlazor/MudBlazor.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015358624"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "65109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KbJW+rhjqdyrTVVpb8Xu8Ix9cV/SmwVkUOesJFPr5w0=\""}, {"Name": "ETag", "Value": "W/\"fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:34:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "_content/MudBlazor/MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "606059"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Jul 2025 21:24:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css.gz", "AssetFile": "_content/MudBlazor/MudBlazor.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "65109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KbJW+rhjqdyrTVVpb8Xu8Ix9cV/SmwVkUOesJFPr5w0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:34:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KbJW+rhjqdyrTVVpb8Xu8Ix9cV/SmwVkUOesJFPr5w0="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "_content/MudBlazor/MudBlazor.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000064616180"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15475"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L2HJaFxT6cjl2vWPEqZI31uHtg9IympiWZsPogeicXE=\""}, {"Name": "ETag", "Value": "W/\"O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:34:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "_content/MudBlazor/MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "73366"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Jul 2025 21:24:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js.gz", "AssetFile": "_content/MudBlazor/MudBlazor.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15475"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L2HJaFxT6cjl2vWPEqZI31uHtg9IympiWZsPogeicXE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:34:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L2HJaFxT6cjl2vWPEqZI31uHtg9IympiWZsPogeicXE="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.sowobu9fea.css", "AssetFile": "_content/MudBlazor/MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "606059"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Jul 2025 21:24:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sowobu9fea"}, {"Name": "integrity", "Value": "sha256-fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.css"}]}, {"Route": "_content/Plotly.Blazor/plotly-interop.js", "AssetFile": "_content/Plotly.Blazor/plotly-interop.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001051524711"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "950"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Nmlv+9WH3GURRPoMLRoyYZSdEp44vzcJ8B6lRoiS7ns=\""}, {"Name": "ETag", "Value": "W/\"ci3mp3ksnNdVXjkTIvalU3BOalXPhFsW8cF7T825Kwo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:34:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ci3mp3ksnNdVXjkTIvalU3BOalXPhFsW8cF7T825Kwo="}]}, {"Route": "_content/Plotly.Blazor/plotly-interop.js", "AssetFile": "_content/Plotly.Blazor/plotly-interop.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5930"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ci3mp3ksnNdVXjkTIvalU3BOalXPhFsW8cF7T825Kwo=\""}, {"Name": "Last-Modified", "Value": "Wed, 22 Nov 2023 14:08:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ci3mp3ksnNdVXjkTIvalU3BOalXPhFsW8cF7T825Kwo="}]}, {"Route": "_content/Plotly.Blazor/plotly-interop.js.gz", "AssetFile": "_content/Plotly.Blazor/plotly-interop.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "950"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Nmlv+9WH3GURRPoMLRoyYZSdEp44vzcJ8B6lRoiS7ns=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:34:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nmlv+9WH3GURRPoMLRoyYZSdEp44vzcJ8B6lRoiS7ns="}]}, {"Route": "_content/Plotly.Blazor/plotly-latest.min.js", "AssetFile": "_content/Plotly.Blazor/plotly-latest.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000000907287"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1102186"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pTEmEGYdn6dn8jOhOWyiIBGo1cbl9ST9fCOcxiCBSzk=\""}, {"Name": "ETag", "Value": "W/\"lXwG1Af3hPEJOnNT2no9Wob5BZDfRHvIMeydqzFesmc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:34:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lXwG1Af3hPEJOnNT2no9Wob5BZDfRHvIMeydqzFesmc="}]}, {"Route": "_content/Plotly.Blazor/plotly-latest.min.js", "AssetFile": "_content/Plotly.Blazor/plotly-latest.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3598601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lXwG1Af3hPEJOnNT2no9Wob5BZDfRHvIMeydqzFesmc=\""}, {"Name": "Last-Modified", "Value": "Wed, 22 Nov 2023 14:08:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lXwG1Af3hPEJOnNT2no9Wob5BZDfRHvIMeydqzFesmc="}]}, {"Route": "_content/Plotly.Blazor/plotly-latest.min.js.gz", "AssetFile": "_content/Plotly.Blazor/plotly-latest.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1102186"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pTEmEGYdn6dn8jOhOWyiIBGo1cbl9ST9fCOcxiCBSzk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:34:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pTEmEGYdn6dn8jOhOWyiIBGo1cbl9ST9fCOcxiCBSzk="}]}, {"Route": "css/app.binpjx7utk.css", "AssetFile": "css/app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001129943503"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "884"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8X7tvwxl8CA4+sQOBJ5TkQX6hu/sjoBIj6qZoJqboQY=\""}, {"Name": "ETag", "Value": "W/\"vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:34:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "binpjx7utk"}, {"Name": "integrity", "Value": "sha256-vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20="}, {"Name": "label", "Value": "css/app.css"}]}, {"Route": "css/app.binpjx7utk.css", "AssetFile": "css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2353"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 05:59:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "binpjx7utk"}, {"Name": "integrity", "Value": "sha256-vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20="}, {"Name": "label", "Value": "css/app.css"}]}, {"Route": "css/app.binpjx7utk.css.gz", "AssetFile": "css/app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "884"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8X7tvwxl8CA4+sQOBJ5TkQX6hu/sjoBIj6qZoJqboQY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:34:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "binpjx7utk"}, {"Name": "integrity", "Value": "sha256-8X7tvwxl8CA4+sQOBJ5TkQX6hu/sjoBIj6qZoJqboQY="}, {"Name": "label", "Value": "css/app.css.gz"}]}, {"Route": "css/app.css", "AssetFile": "css/app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001129943503"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "884"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8X7tvwxl8CA4+sQOBJ5TkQX6hu/sjoBIj6qZoJqboQY=\""}, {"Name": "ETag", "Value": "W/\"vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:34:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20="}]}, {"Route": "css/app.css", "AssetFile": "css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2353"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 05:59:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20="}]}, {"Route": "css/app.css.gz", "AssetFile": "css/app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "884"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8X7tvwxl8CA4+sQOBJ5TkQX6hu/sjoBIj6qZoJqboQY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:34:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8X7tvwxl8CA4+sQOBJ5TkQX6hu/sjoBIj6qZoJqboQY="}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002169197397"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "460"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ssNxTluBIJD0rC4aNiKH9A8Kv/9vBorV8vSIggZXatk=\""}, {"Name": "ETag", "Value": "W/\"3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:34:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "892"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 12:45:24 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "460"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ssNxTluBIJD0rC4aNiKH9A8Kv/9vBorV8vSIggZXatk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:34:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ssNxTluBIJD0rC4aNiKH9A8Kv/9vBorV8vSIggZXatk="}]}, {"Route": "index.z0kvmgt2ev.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002169197397"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "460"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ssNxTluBIJD0rC4aNiKH9A8Kv/9vBorV8vSIggZXatk=\""}, {"Name": "ETag", "Value": "W/\"3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:34:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0kvmgt2ev"}, {"Name": "integrity", "Value": "sha256-3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.z0kvmgt2ev.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "892"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 12:45:24 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0kvmgt2ev"}, {"Name": "integrity", "Value": "sha256-3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.z0kvmgt2ev.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "460"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ssNxTluBIJD0rC4aNiKH9A8Kv/9vBorV8vSIggZXatk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:34:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0kvmgt2ev"}, {"Name": "integrity", "Value": "sha256-ssNxTluBIJD0rC4aNiKH9A8Kv/9vBorV8vSIggZXatk="}, {"Name": "label", "Value": "index.html.gz"}]}]}