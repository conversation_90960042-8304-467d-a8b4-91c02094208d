{"accessibility": {"captions": {"common_models_path": "", "soda_binary_path": ""}}, "autofill": {"ablation_seed": "2tCcG9wjrx0="}, "breadcrumbs": {"enabled": true, "enabled_time": "13397052460018420"}, "default_browser": {"browser_name_enum": 13}, "desktop_session_duration_tracker": {"last_session_end_timestamp": "1752667075"}, "domain_actions_config": "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", "edge": {"manageability": {"edge_last_active_time": "13397140672759016"}, "mitigation_manager": {"renderer_app_container_compatible_count": 1, "renderer_code_integrity_compatible_count": 1}, "tab_stabs": {"closed_without_unfreeze_never_unfrozen": 0, "closed_without_unfreeze_previously_unfrozen": 0, "discard_without_unfreeze_never_unfrozen": 0, "discard_without_unfreeze_previously_unfrozen": 0}, "tab_stats": {"frozen_daily": 0, "unfrozen_daily": 0}}, "edge_ci": {"metrics_bookmark": "<BookmarkList>\r\n</BookmarkList>", "num_healthy_browsers_since_failure": 2}, "hardware_acceleration_mode_previous": true, "identity_combined_status": {"aad": 1, "ad": 1}, "legacy": {"profile": {"name": {"migrated": true}}}, "local": {"password_hash_data_list": []}, "network_time": {"network_time_mapping": {"local": 1752666924900.453, "network": 1752666925000.0, "ticks": ************.0, "uncertainty": 1773513.0}}, "optimization_guide": {"model_execution": {"last_usage_by_feature": {}}, "model_store_metadata": {}, "on_device": {"last_version": "138.0.3351.83", "model_crash_count": 0}}, "os_crypt": {"audit_enabled": true, "encrypted_key": "RFBBUEkBAAAA0Iyd3wEV0RGMegDAT8KX6wEAAAAbAdjfXo7TTb4d1fC7X1qfEAAAAB4AAABNAGkAYwByAG8AcwBvAGYAdAAgAEUAZABnAGUAAAAQZgAAAAEAACAAAADVWmCN89xFg+Nt5vjvbJGgp6sD8FCqIgoqBVOQqfB+jwAAAAAOgAAAAAIAACAAAACCaCnQIPIkFXOVDn0BQ1TFqpAK9xwbTbk4/GEHpNZaJDAAAAAMRLj4la1Mjl55qDssdLzzhRjNe2KjdyNek8xjcwQuqYq/cUqRybVfz/FJyg5ezzJAAAAAmPP4Vo6tYO6+0De4UFBq1MJevjuP6UI73vau79vMg1AOqX1I/qUvRdODdQTSkciFl+nhAQoIbbUDGTd2xZX5lQ=="}, "performance_intervention": {"last_daily_sample": "*****************"}, "policy": {"last_statistics_update": "*****************"}, "profile": {"info_cache": {"Default": {"active_time": **********.270937, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_20", "background_apps": false, "edge_account_cid": "", "edge_account_environment": 0, "edge_account_environment_string": "", "edge_account_first_name": "", "edge_account_last_name": "", "edge_account_oid": "", "edge_account_sovereignty": 0, "edge_account_tenant_id": "", "edge_account_type": 0, "edge_profile_can_be_deleted": true, "edge_test_on_premises": false, "edge_wam_aad_for_app_account_type": 0, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_given_name": "", "gaia_id": "", "gaia_name": "", "hosted_domain": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_glic_eligible": false, "is_using_default_avatar": true, "is_using_default_name": true, "managed_user_id": "", "metrics_bucket_index": 1, "name": "Profile 1", "signin.with_credential_provider": false, "user_name": ""}}, "last_active_profiles": ["<PERSON><PERSON><PERSON>"], "metrics": {"next_bucket_index": 2}, "profile_counts_reported": "*****************", "profiles_order": ["<PERSON><PERSON><PERSON>"]}, "profile_network_context_service": {"http_cache_finch_experiment_groups": "None None None None"}, "profiles": {"edge": {"guided_switch_pref": [], "multiple_profiles_with_same_account": false}, "edge_sso_info": {"msa_first_profile_key": "<PERSON><PERSON><PERSON>", "msa_sso_algo_state": 1}, "signin_last_seen_version": "138.0.3351.83", "signin_last_updated_time": **********.561813}, "sentinel_creation_time": "0", "session_id_generator_last_value": "**********", "signin": {"active_accounts_last_emitted": "*****************"}, "startup_boost": {"last_browser_open_time": "*****************"}, "subresource_filter": {"ruleset_version": {"checksum": **********, "content": "**********", "format": 36}}, "tab_stats": {"discards_external": 0, "discards_proactive": 0, "discards_urgent": 0, "last_daily_sample": "*****************", "max_tabs_per_window": 1, "reloads_external": 0, "reloads_urgent": 0, "total_tab_count_max": 1, "window_count_max": 1}, "telemetry_client": {"cloned_install": {"user_data_dir_id": 9229173}, "governance": {"last_dma_change_date": "*****************", "last_known_cps": 0}, "host_telclient_path": "QzpcUHJvZ3JhbSBGaWxlcyAoeDg2KVxNaWNyb3NvZnRcRWRnZVdlYlZpZXdcQXBwbGljYXRpb25cMTM4LjAuMzM1MS44M1x0ZWxjbGllbnQuZGxs", "sample_id": ********}, "uninstall_metrics": {"installation_date2": "**********"}, "updateclientdata": {"apps": {"ahmaebgpfccdhgidjaidaoojjcijckba": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "*******"}, "alpjnmnfbgfkmmpcfpejmmoebdndedno": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "********", "pv": "********"}, "eeobbhfgfagbclfofmgbdfoicabjdbkn": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "*******"}, "fgbafbciocncjfbbonhocjaohoknlaco": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "2025.5.15.1"}, "fppmbhmldokgmleojlplaaodlkibgikh": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "********"}, "hajigopbbjhghbfimgkfmpenfkclmohk": {"cohort": "", "cohortname": "", "installdate": -1}, "jbfaflocpnkhbgcijpkiafdpbjkedane": {"cohort": "", "cohortname": "", "installdate": -1}, "kpfehajjjbbcifeehjgfgnabifknmdad": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "120.0.6050.0"}, "ldfkbgjbencjpgjfleiooeldhjdapggh": {"cohort": "", "cohortname": "", "installdate": -1}, "mcfjlbnicoclaecapilmleaelokfnijm": {"cohort": "", "cohortname": "", "installdate": -1}, "ndikpojcjlepofdkaaldkinkjbeeebkl": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "**********"}, "oankkpibpaokgecfckkdkgaoafllipag": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "6498.2024.12.2"}, "ohckeflnhegojcjlcpbfpciadgikcohk": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "*******"}, "ojblfafjmiikbkepnnolpgbbhejhlcim": {"cohort": "", "cohortname": "", "installdate": -1}, "pghocgajpebopihickglahgebcmkcekh": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "********"}, "pmagihnlncbcefglppponlgakiphldeh": {"cohort": "", "cohortname": "", "installdate": -1}}}, "updateclientlastupdatecheckerror": 0, "updateclientlastupdatecheckerrorcategory": 0, "updateclientlastupdatecheckerrorextracode1": 0, "user_experience_metrics": {"client_id2": "{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}C:\\Users\\<USER>", "client_id_timestamp": "**********", "diagnostics": {"last_data_collection_level_on_launch": 3}, "initial_logs2": [], "last_seen": {"CrashpadMetrics": "0"}, "limited_entropy_randomization_source": "EC28CBD968A5457419FD9733558AB555", "log_finalized_record_id": 3, "log_record_id": 4, "low_entropy_source3": 5176, "machine_id": 6460531, "ongoing_logs2": [], "payload_counter": 2, "pseudo_low_entropy_source": 5994, "reporting_enabled": true, "reset_client_id_deterministic": true, "session_id": 1, "stability": {"browser_last_live_timestamp": "13397140522203277", "exited_cleanly": true, "saved_system_profile": "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", "saved_system_profile_hash": "2CD9F9161CD1680C68860C88220195DDF25FE6D5", "stats_buildtime": "1752102914", "stats_version": "138.0.3351.83-64", "system_crash_count": 0}, "unsent_log_metadata": {"initial_logs": {"sent_samples_count": 0, "unsent_persisted_size_in_kb": 0, "unsent_samples_count": 0}, "ongoing_logs": {"sent_samples_count": 0, "unsent_persisted_size_in_kb": 0, "unsent_samples_count": 0}}}, "variations_compressed_seed": "safe_seed_content", "variations_config_ids": "{\"ECS\":\"P-R-1082570-1-11,P-D-42388-2-6\",\"EdgeConfig\":\"P-R-1627497-3-8,P-R-1612140-3-4,P-R-1541171-6-9,P-R-1528200-3-4,P-R-1480299-3-5,P-R-1459857-3-2,P-R-1459074-3-9,P-R-1447912-3-12,P-R-1315481-1-6,P-R-1253933-3-8,P-R-1160552-1-3,P-R-1133477-3-4,P-R-1113531-4-9,P-R-1082109-3-6,P-R-1054140-1-4,P-R-1049918-1-3,P-R-68474-9-12,P-R-60617-8-21,P-R-45373-8-85\",\"EdgeFirstRunConfig\":\"P-R-1253659-3-4,P-R-1075865-4-7\",\"Segmentation\":\"P-R-1473016-1-8,P-R-1159985-1-5,P-R-1113915-25-11,P-R-1098334-1-6,P-R-66078-1-3,P-R-66077-1-5,P-R-60882-1-2,P-R-43082-3-5,P-R-42744-1-2\"}", "variations_country": "NG", "variations_crash_streak": 0, "variations_failed_to_fetch_seed_streak": 1, "variations_google_groups": {"Default": []}, "variations_last_fetch_time": "13397140523567950", "variations_last_runtime_fetch_time": "13397140525231391", "variations_limited_entropy_synthetic_trial_seed_v2": "67", "variations_permanent_consistency_country": ["138.0.3351.83", "NG"], "variations_runtime_compressed_seed": "H4sIAAAAAAAAAG2QS4/TQBCE/0uf3WJ6ep6WOKAkbABpk30gZYU5GGeIgrANtiMUIv/31XiS7EpwrO6qmf7qBLO2+b7ffZj3kMOpgMXsoYC8gDXeIwkntRVISJStcY5KsnMo0RSQFbDY7sK8rct9864a9m3Tvwp6lkrHoM/SgK1nEwfqPJDKeL48Pf1lhEJCNpO2jsXkd0mykZO0SZKQGjUST9IwGY0KZcpqlopRYvIqITwjX5bsrFJo0actk/MepUfl1cRIpJ0hhRIvmh2Ti5ddqe8PzbCvQ6ruFTU7awRK9JPzIezq0Axl7ObFpCwLiigJjEh772JV+qyJPWmU+qUZ75hjNQnAGGGnc67KXtNGOCeRUCZyFk4in3dKRnBCWcAI2b8ckJ9gUf8ajv/dhKb89jO8D+Vw6EIP+RcIoavg6zhmsAzlNnR9tM3aQzN0x1m7DZDD7U386LHcQQ4FfP74Y+U2a1rS7z83T1W1XNXBVn9vj6vHdXijAt3dPW26zafA/dsCYByfAR/dDcadAgAA", "variations_safe_compressed_seed": "H4sIAAAAAAAAAJVW25LaOBD9lSk9W1OW72ZrHxguCbuwyw4QUtmkpoTdGNXYkkuSB0hq/n1LtmFgMs4mPFB065zTF7Ubf0OjwQL1vqHRIcmrFEYHDZLTfCD4lmWTVE34VGSop2UFFmq8U5EtqcxAox6CNIMHpekmB/RsoVGaQQOqNdMMxiyHxVFpKPpJAkoZf8qUIYyB6kqCQr1/0WvY4lhsRM6SKeOPgx0kj+jLs4X+ZHm+2DOd7PBK0QxGT8D1kGraoVqoCa/LukZP+Bgg3dBWddgSX6VQh82Z0j+Z8pBJSLSQx4kGSTUT/C4XjcJLBSNuZNYfnH6aMoOh+VwKQze1QbqEHArQ8miCAv++oDVsPjDY/z//orL1B2fJ+HHNeCr290Bzpo9NSl1t64BbnUd9nq7KlGpoqiwO/ZItd1JonYPJTNW31hnvmvAq9wEtQNL6CmeCMy0k41mH1AxSRsei4ml9A29T61Fa3NHkMZMGeg9KVDKBMXTn2IU2WvsnZ0F5uhGHDvJEiZxqaEGQTraSFqCuJ+IeeAoSZL8sB4JryjjIzoadxqBhv0k14msh83RQleNcUH08DXptzEQKnfIXECMzFTRdaKqZ0ixRU5FljGdzyTr5rwn7uWQtrRZcpMmuSb1rzs3uMKimDNj0K72bU6Ue4ahWk3rY5eJHQ9VygGuW1LPwwm76bgLsRFk2pSQwEEVJJdyDKgVXF2vsrfROzBUt2ehQkh9K9tMp5dnfTyAlS39N2K2F/1lNBoZQUjM3GqQBf0OcFoB6KNlRziFHFnqieWU8Y/RsnY+hFMnu4tC1m88lRoKWlKuC6XqVPwj+sGcSHjQrQFT6oWB5zhQkgqfqQsoxKibBU231ilWdd1qf9vNc7Kdmr36xOipqoSfVGvwSdcYSKZTY6ts1bO6k2CuQt3MptNhU29vVn7PbZi3PpdiyHCzbIv5vP0HSVKvbqcgWmkpt2Zb3KyRz1TloqHlNU867eC7KBTXH9Xe7vN5q0FjIBM60M9qqn6hsCUqvONsKWQyZ0pJtKjPX75nSIpO06O6nWVylc07j6gYvxyCFLa1y/c7A30JfgavHolPyy3P7OIyZVPq+4i9DPwTTpvH9qL2eehwgndNjLmjauVE6aO3fTamPb0f7vssAW5mgOsEFZAVwXW8HA571J/08Rz20pbkyJa3Xo2uHSsptZTx2/TurqExPxpNZ17aFZm7gD4SEa6bZy8D1AuQTS0C9igObj8tr1x3j2bXHFHjtOY/nSpnYrf/ZQu+BpvUIfEOjJc1QD31Gn8rRp2j4daUOg8HIuztoXR0+Vp94NDrMJ4MNVf3xfL3w57Ovs98/IxPvULK6Z2hZgXVD/Js/qvzGsR3/hjg9J+x55ObdbFnXVnEtj4P6HwX99Q5ZyDwXlWo99Zo4vT1Ohk1e5rUTzfE9Jnbk+KGNCSbEmuMh9hw3irCDA3T9QtmgAyf04hC7OLIamzjEs7GLvcb2PUJCggMct7YTOfbFuRfZThxjF/ut7ceRb/Scs22HHnZPfM8LY+JgF5MW4BLfiwgmOGhsx3dj131JiAS27zuYYLe1XdcLw5cECHF9l2DvFMCOHGKbhFo92/dMQeSEt704JtFZL4i80MPxKZ3ADkiII+yQ2vR8N3RxhCMfvf0AolPOgR+/5GSHfhT42MMhev1YNAQvdG0SYHIu0o/jyMfk1EVC3Jj42PGbSzSSceS63rlNQWCHF0UEdhie2YEdRaZhTUWea0fO+X48J/SMioOen/8D5f0i4a0MAAA=", "variations_safe_seed_date": "13397052460000000", "variations_safe_seed_fetch_time": "13397140523567950", "variations_safe_seed_locale": "en-GB", "variations_safe_seed_milestone": 138, "variations_safe_seed_permanent_consistency_country": "NG", "variations_safe_seed_session_consistency_country": "NG", "variations_safe_seed_signature": "", "variations_seed_client_version_at_store": "138.0.3351.83", "variations_seed_date": "13397140523000000", "variations_seed_etag": "\"ZpEZ8DzUsxCCE4BxttuxXuZn8ExPICbasAFPWS5PMzM=\"", "variations_seed_milestone": 138, "variations_seed_runtime_etag": "\"UJjO8XP1H1qwGYccHOme7czNyOTPe/4e1QQYXrXKe3s=\"", "variations_seed_signature": "", "was": {"restarted": false}}