{"Version": 1, "Hash": "wLOnE9CvcjqKLmf+eb9id28qTRRq00inFd22qN9TpLM=", "Source": "AcumeniDashboard", "BasePath": "/", "Mode": "Root", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "AcumeniDashboard\\wwwroot", "Source": "AcumeniDashboard", "ContentRoot": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\", "BasePath": "/", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\0wz98yz2xy-b8x8f7e52z.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z2p08kpgbn", "Integrity": "BRlqmf2WzpTbcePWG15cPM4g6sa7ssVZeZ5ZBmZqjng=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "FileLength": 15402, "LastWriteTime": "2025-07-16T11:51:42+00:00"}, {"Identity": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\laa0xb5mlh-z0kvmgt2ev.gz", "SourceId": "AcumeniDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint=z0kvmgt2ev}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uir425lx1n", "Integrity": "qpVkyAIMcFSxTh5Cpf5zIt85ppSJsCUc2TbFwmyYVYw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\index.html", "FileLength": 459, "LastWriteTime": "2025-07-16T11:51:42+00:00"}, {"Identity": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\mnb9s2pngy-hhex5g9j6i.gz", "SourceId": "Plotly<PERSON>", "SourceType": "Package", "ContentRoot": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "_content/Plotly.Blazor", "RelativePath": "plotly-latest.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\plotly.blazor\\4.1.0\\staticwebassets\\plotly-latest.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jojcie3vcz", "Integrity": "oyh6wUVsSoZqJUSJMPmkaKsT9bn0+yHo/4ZF9D3xSvg=", "CopyToOutputDirectory": "Always", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\plotly.blazor\\4.1.0\\staticwebassets\\plotly-latest.min.js", "FileLength": 1089015, "LastWriteTime": "2025-07-15T08:18:36+00:00"}, {"Identity": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\tzxjg6is5z-sowobu9fea.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "59wrnbo615", "Integrity": "96Cl4EXJY5eN8ZZxPJLgMEvyRaW3jdF08SbOpeIwjjc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "FileLength": 65487, "LastWriteTime": "2025-07-16T11:51:42+00:00"}, {"Identity": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\vz72gntm4v-binpjx7utk.gz", "SourceId": "AcumeniDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint=binpjx7utk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\css\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s5poaa14z9", "Integrity": "YFfbIZf4TmlhMo3a8orCQH20ox12ZMXY/R5MbNoLxFc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\css\\app.css", "FileLength": 882, "LastWriteTime": "2025-07-15T08:18:35+00:00"}, {"Identity": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\xtrujjgkm4-aieg4310hh.gz", "SourceId": "Plotly<PERSON>", "SourceType": "Package", "ContentRoot": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\", "BasePath": "_content/Plotly.Blazor", "RelativePath": "plotly-interop.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\plotly.blazor\\4.1.0\\staticwebassets\\plotly-interop.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hd0nw6jzyh", "Integrity": "pUnL2/1ZBINTh623Tiwx6RYGonvvhEdlk6KgQRek+1g=", "CopyToOutputDirectory": "Always", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\plotly.blazor\\4.1.0\\staticwebassets\\plotly-interop.js", "FileLength": 953, "LastWriteTime": "2025-07-15T08:18:35+00:00"}, {"Identity": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\css\\app.css", "SourceId": "AcumeniDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "binpjx7utk", "Integrity": "vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 2353, "LastWriteTime": "2025-07-15T05:59:21+00:00"}, {"Identity": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\index.html", "SourceId": "AcumeniDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0kvmgt2ev", "Integrity": "3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 892, "LastWriteTime": "2025-07-15T12:45:24+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sowobu9fea", "Integrity": "fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "FileLength": 606059, "LastWriteTime": "2025-07-01T21:24:18+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b8x8f7e52z", "Integrity": "O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "FileLength": 73366, "LastWriteTime": "2025-07-01T21:24:18+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\plotly.blazor\\4.1.0\\staticwebassets\\plotly-interop.js", "SourceId": "Plotly<PERSON>", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\plotly.blazor\\4.1.0\\staticwebassets\\", "BasePath": "_content/Plotly.Blazor", "RelativePath": "plotly-interop.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "aieg4310hh", "Integrity": "ci3mp3ksnNdVXjkTIvalU3BOalXPhFsW8cF7T825Kwo=", "CopyToOutputDirectory": "Always", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\plotly.blazor\\4.1.0\\staticwebassets\\plotly-interop.js", "FileLength": 5930, "LastWriteTime": "2023-11-22T14:08:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\plotly.blazor\\4.1.0\\staticwebassets\\plotly-latest.min.js", "SourceId": "Plotly<PERSON>", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\plotly.blazor\\4.1.0\\staticwebassets\\", "BasePath": "_content/Plotly.Blazor", "RelativePath": "plotly-latest.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hhex5g9j6i", "Integrity": "lXwG1Af3hPEJOnNT2no9Wob5BZDfRHvIMeydqzFesmc=", "CopyToOutputDirectory": "Always", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\plotly.blazor\\4.1.0\\staticwebassets\\plotly-latest.min.js", "FileLength": 3598601, "LastWriteTime": "2023-11-22T14:08:48+00:00"}], "Endpoints": [{"Route": "_content/MudBlazor/MudBlazor.min.b8x8f7e52z.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "73366"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Jul 2025 21:24:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b8x8f7e52z"}, {"Name": "integrity", "Value": "sha256-O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.js"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\tzxjg6is5z-sowobu9fea.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015269973"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "65487"}, {"Name": "ETag", "Value": "\"96Cl4EXJY5eN8ZZxPJLgMEvyRaW3jdF08SbOpeIwjjc=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 11:51:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "606059"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Jul 2025 21:24:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css.gz", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\tzxjg6is5z-sowobu9fea.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "65487"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"96Cl4EXJY5eN8ZZxPJLgMEvyRaW3jdF08SbOpeIwjjc=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 11:51:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-96Cl4EXJY5eN8ZZxPJLgMEvyRaW3jdF08SbOpeIwjjc="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\0wz98yz2xy-b8x8f7e52z.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000064922418"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15402"}, {"Name": "ETag", "Value": "\"BRlqmf2WzpTbcePWG15cPM4g6sa7ssVZeZ5ZBmZqjng=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 11:51:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "73366"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Jul 2025 21:24:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js.gz", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\0wz98yz2xy-b8x8f7e52z.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15402"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BRlqmf2WzpTbcePWG15cPM4g6sa7ssVZeZ5ZBmZqjng=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 11:51:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BRlqmf2WzpTbcePWG15cPM4g6sa7ssVZeZ5ZBmZqjng="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.sowobu9fea.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "606059"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Jul 2025 21:24:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sowobu9fea"}, {"Name": "integrity", "Value": "sha256-fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.css"}]}, {"Route": "_content/Plotly.Blazor/plotly-interop.js", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\xtrujjgkm4-aieg4310hh.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001048218029"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "953"}, {"Name": "ETag", "Value": "\"pUnL2/1ZBINTh623Tiwx6RYGonvvhEdlk6KgQRek+1g=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 08:18:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"ci3mp3ksnNdVXjkTIvalU3BOalXPhFsW8cF7T825Kwo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ci3mp3ksnNdVXjkTIvalU3BOalXPhFsW8cF7T825Kwo="}]}, {"Route": "_content/Plotly.Blazor/plotly-interop.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\plotly.blazor\\4.1.0\\staticwebassets\\plotly-interop.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5930"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ci3mp3ksnNdVXjkTIvalU3BOalXPhFsW8cF7T825Kwo=\""}, {"Name": "Last-Modified", "Value": "Wed, 22 Nov 2023 14:08:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ci3mp3ksnNdVXjkTIvalU3BOalXPhFsW8cF7T825Kwo="}]}, {"Route": "_content/Plotly.Blazor/plotly-interop.js.gz", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\xtrujjgkm4-aieg4310hh.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "953"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pUnL2/1ZBINTh623Tiwx6RYGonvvhEdlk6KgQRek+1g=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 08:18:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pUnL2/1ZBINTh623Tiwx6RYGonvvhEdlk6KgQRek+1g="}]}, {"Route": "_content/Plotly.Blazor/plotly-latest.min.js", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\mnb9s2pngy-hhex5g9j6i.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000000918260"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1089015"}, {"Name": "ETag", "Value": "\"oyh6wUVsSoZqJUSJMPmkaKsT9bn0+yHo/4ZF9D3xSvg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 08:18:36 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"lXwG1Af3hPEJOnNT2no9Wob5BZDfRHvIMeydqzFesmc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lXwG1Af3hPEJOnNT2no9Wob5BZDfRHvIMeydqzFesmc="}]}, {"Route": "_content/Plotly.Blazor/plotly-latest.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\plotly.blazor\\4.1.0\\staticwebassets\\plotly-latest.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3598601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lXwG1Af3hPEJOnNT2no9Wob5BZDfRHvIMeydqzFesmc=\""}, {"Name": "Last-Modified", "Value": "Wed, 22 Nov 2023 14:08:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lXwG1Af3hPEJOnNT2no9Wob5BZDfRHvIMeydqzFesmc="}]}, {"Route": "_content/Plotly.Blazor/plotly-latest.min.js.gz", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\mnb9s2pngy-hhex5g9j6i.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1089015"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oyh6wUVsSoZqJUSJMPmkaKsT9bn0+yHo/4ZF9D3xSvg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 08:18:36 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oyh6wUVsSoZqJUSJMPmkaKsT9bn0+yHo/4ZF9D3xSvg="}]}, {"Route": "css/app.binpjx7utk.css", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\vz72gntm4v-binpjx7utk.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001132502831"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "882"}, {"Name": "ETag", "Value": "\"YFfbIZf4TmlhMo3a8orCQH20ox12ZMXY/R5MbNoLxFc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 08:18:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "binpjx7utk"}, {"Name": "label", "Value": "css/app.css"}, {"Name": "integrity", "Value": "sha256-vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20="}]}, {"Route": "css/app.binpjx7utk.css", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2353"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 05:59:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "binpjx7utk"}, {"Name": "label", "Value": "css/app.css"}, {"Name": "integrity", "Value": "sha256-vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20="}]}, {"Route": "css/app.binpjx7utk.css.gz", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\vz72gntm4v-binpjx7utk.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "882"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YFfbIZf4TmlhMo3a8orCQH20ox12ZMXY/R5MbNoLxFc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 08:18:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "binpjx7utk"}, {"Name": "label", "Value": "css/app.css.gz"}, {"Name": "integrity", "Value": "sha256-YFfbIZf4TmlhMo3a8orCQH20ox12ZMXY/R5MbNoLxFc="}]}, {"Route": "css/app.css", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\vz72gntm4v-binpjx7utk.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001132502831"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "882"}, {"Name": "ETag", "Value": "\"YFfbIZf4TmlhMo3a8orCQH20ox12ZMXY/R5MbNoLxFc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 08:18:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20="}]}, {"Route": "css/app.css", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2353"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 05:59:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vbVlR3qOf4iI+RHc42U+FFOkNO8OSzG9YynURXPyA20="}]}, {"Route": "css/app.css.gz", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\vz72gntm4v-binpjx7utk.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "882"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YFfbIZf4TmlhMo3a8orCQH20ox12ZMXY/R5MbNoLxFc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 08:18:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YFfbIZf4TmlhMo3a8orCQH20ox12ZMXY/R5MbNoLxFc="}]}, {"Route": "index.html", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\laa0xb5mlh-z0kvmgt2ev.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002173913043"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "459"}, {"Name": "ETag", "Value": "\"qpVkyAIMcFSxTh5Cpf5zIt85ppSJsCUc2TbFwmyYVYw=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 11:51:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo="}]}, {"Route": "index.html", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "892"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 12:45:24 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\laa0xb5mlh-z0kvmgt2ev.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "459"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"qpVkyAIMcFSxTh5Cpf5zIt85ppSJsCUc2TbFwmyYVYw=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 11:51:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qpVkyAIMcFSxTh5Cpf5zIt85ppSJsCUc2TbFwmyYVYw="}]}, {"Route": "index.z0kvmgt2ev.html", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\laa0xb5mlh-z0kvmgt2ev.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002173913043"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "459"}, {"Name": "ETag", "Value": "\"qpVkyAIMcFSxTh5Cpf5zIt85ppSJsCUc2TbFwmyYVYw=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 11:51:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0kvmgt2ev"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo="}]}, {"Route": "index.z0kvmgt2ev.html", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "892"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 12:45:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0kvmgt2ev"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-3SJQh3Xe/d+Oj3tHo3NN8imU2tdq9oIeZwLxLMUnuVo="}]}, {"Route": "index.z0kvmgt2ev.html.gz", "AssetFile": "C:\\Studio\\AppStudio\\ESJ Software\\AcumeniDashboard\\AcumeniDashboard\\obj\\x64\\Debug\\net9.0-maccatalyst\\maccatalyst-x64\\compressed\\laa0xb5mlh-z0kvmgt2ev.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "459"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"qpVkyAIMcFSxTh5Cpf5zIt85ppSJsCUc2TbFwmyYVYw=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 11:51:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0kvmgt2ev"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-qpVkyAIMcFSxTh5Cpf5zIt85ppSJsCUc2TbFwmyYVYw="}]}]}