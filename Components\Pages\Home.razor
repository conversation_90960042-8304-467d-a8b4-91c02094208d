@page "/"
@using MudBlazor
@using System.Globalization
@using AcumeniDashboard.Services
@using AcumeniDashboard.Models
@inject IDataAnalyzerService DataAnalyzer
@inject ConfigurationService Config
@implements IDisposable

<PageTitle>Acumeni Dashboard</PageTitle>

<style>
    .selected-row {
        background-color: var(--mud-palette-primary-lighten) !important;
        color: var(--mud-palette-primary-text) !important;
    }
    
    .selected-row:hover {
        background-color: var(--mud-palette-primary) !important;
    }
</style>

@* 
    EXACT CONVERSION FROM PYTHON: Acumeni_Dashboard.py
    Converting DashboardApp.setup_layout() method line by line
    Python lines 1207-1400+ converted to Blazor Hybrid
*@

@* Python Line 1207: self.app.layout = dbc.Container([ *@
<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-4">

    @* Python Lines 1208-1213: Header Row with Title and Subtitle *@
    <MudGrid Class="mb-4">
        <MudItem xs="12">
            @* Python Line 1210: html.H1(id="dashboard-title", children=DASHBOARD_TITLE, className="text-center my-4") *@
            <MudText id="dashboard-title" Typo="Typo.h3" Align="Align.Center" Class="my-4">
                Hospital Voice of Customer Dashboard
            </MudText>
            @* Python Line 1211: html.P(id="dashboard-subtitle", children=DASHBOARD_SUBTITLE, className="text-center text-muted mb-5") *@
            <MudText id="dashboard-subtitle" Typo="Typo.subtitle1" Align="Align.Center" Color="Color.Secondary" Class="text-muted mb-5">
                Real-time analysis of hospital survey responses
            </MudText>
        </MudItem>
    </MudGrid>

    @* Python Lines 1215-1242: First Row - KPI Cards (Last Update, CSAT, NPS) *@
    <MudGrid Class="mb-4">
        @* Python Lines 1216-1223: Last Data Update Card *@
        <MudItem xs="12" md="4">
            <MudCard Class="shadow" Style="height: 150px;">
                <MudCardContent Class="text-center d-flex flex-column justify-center" Style="height: 100%;">
                    @* Python Line 1219: html.H4("Last Data Update", className="card-title text-center") *@
                    <MudText Typo="Typo.h6" Class="card-title text-center mb-3">Last Data Update</MudText>
                    @* Python Line 1220: html.P(id="last-update-time", className="card-text text-center fs-4") *@
                    <MudText id="last-update-time" Typo="Typo.h5" Class="card-text text-center">
                        @LastUpdated.ToString("yyyy-MM-dd HH:mm:ss")
                    </MudText>
                </MudCardContent>
            </MudCard>
        </MudItem>

        @* Python Lines 1225-1232: CSAT Score Card *@
        <MudItem xs="12" md="4">
            <MudCard Class="shadow" Style="height: 150px;">
                <MudCardContent Class="text-center d-flex flex-column justify-center" Style="height: 100%;">
                    @* Python Line 1228: html.H4("CSAT Score", className="card-title text-center") *@
                    <MudText Typo="Typo.h6" Class="card-title text-center mb-3">CSAT Score</MudText>
                    @* Python Line 1229: html.P(id="avg-csat", className="card-text text-center fs-1 fw-bold") *@
                    <MudText id="avg-csat" Typo="Typo.h3" Class="card-text text-center fw-bold">
                        @(DashboardData?.CsatAverage > 0 ? DashboardData.CsatAverage.ToString("F1") : "N/A")
                    </MudText>
                </MudCardContent>
            </MudCard>
        </MudItem>

        @* Python Lines 1234-1241: Net Promoter Score Card *@
        <MudItem xs="12" md="4">
            <MudCard Class="shadow" Style="height: 150px;">
                <MudCardContent Class="text-center d-flex flex-column justify-center" Style="height: 100%;">
                    @* Python Line 1237: html.H4("Net Promoter Score", className="card-title text-center") *@
                    <MudText Typo="Typo.h6" Class="card-title text-center mb-3">Net Promoter Score</MudText>
                    @* Python Line 1238: html.P(id="nps-score", className="card-text text-center fs-1 fw-bold") *@
                    <MudText id="nps-score" Typo="Typo.h3" Class="card-text text-center fw-bold" Color="@GetNpsColor()">
                        @(DashboardData?.NpsScore != 0 ? DashboardData?.NpsScore.ToString("F0") : "N/A")
                    </MudText>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    @* Python Lines 1244-1262: Second Row - CSAT and NPS Breakdown Charts *@
    <MudGrid Class="mb-4">
        @* Python Lines 1245-1252: CSAT Breakdown Chart *@
        <MudItem xs="12" md="6">
            <MudCard Class="shadow">
                @* Python Line 1247: dbc.CardHeader(html.H5("CSAT Breakdown")) *@
                <MudCardHeader>
                    <MudText Typo="Typo.h6">CSAT Breakdown</MudText>
                </MudCardHeader>
                <MudCardContent>
                    @* Python Line 1249: dcc.Graph(id="csat-breakdown") - Enhanced vertical bar chart with grid lines *@
                    <div id="csat-breakdown" style="height: 300px; padding: 20px; position: relative;">
                        @if (DashboardData?.CsatBreakdown != null)
                        {
                            @* Grid Lines Background *@
                            <div style="position: absolute; top: 20px; left: 20px; right: 20px; bottom: 60px; pointer-events: none;">
                                @* Horizontal Grid Lines *@
                                @for (int i = 0; i <= 5; i++)
                                {
                                    var topPercent = i * 20;
                                    <div style="position: absolute; top: @(topPercent)%; left: 0; right: 0; height: 1px; background-color: #e0e0e0; opacity: 0.7;"></div>
                                    <div style="position: absolute; top: @(topPercent)%; left: -15px; font-size: 10px; color: #666;">@(100 - topPercent)%</div>
                                }
                                @* Vertical Grid Lines *@
                                @for (int i = 0; i <= 3; i++)
                                {
                                    var leftPercent = i * 33.33;
                                    <div style="position: absolute; left: @(leftPercent)%; top: 0; bottom: 0; width: 1px; background-color: #e0e0e0; opacity: 0.5;"></div>
                                }
                            </div>

                            <div style="height: 100%; display: flex; align-items: end; justify-content: space-around; position: relative;">
                                @* Positive Bar *@
                                <div style="display: flex; flex-direction: column; align-items: center; height: 100%;">
                                    <div style="display: flex; flex-direction: column; justify-content: end; height: 200px; width: 60px; position: relative;">
                                        @{
                                            var positivePercent = GetPercentage(DashboardData.CsatBreakdown.Positive, DashboardData.CsatBreakdown.Positive + DashboardData.CsatBreakdown.Neutral + DashboardData.CsatBreakdown.Negative);
                                            var positiveHeight = positivePercent * 2;
                                        }
                                        <div style="background-color: var(--mud-palette-success); width: 100%; border-radius: 4px 4px 0 0; height: @(positiveHeight)px; position: relative;">
                                            @* Value on top of bar *@
                                            <div style="position: absolute; top: -20px; left: 50%; transform: translateX(-50%); font-size: 12px; font-weight: bold; color: var(--mud-palette-success);">
                                                @positivePercent.ToString("F0")%
                                            </div>
                                        </div>
                                    </div>
                                    <MudText Typo="Typo.h6" Color="Color.Success" Class="mt-2">@DashboardData.CsatBreakdown.Positive</MudText>
                                    <MudText Typo="Typo.body2" Class="text-center">Positive<br/>(7-10)</MudText>
                                </div>

                                @* Neutral Bar *@
                                <div style="display: flex; flex-direction: column; align-items: center; height: 100%;">
                                    <div style="display: flex; flex-direction: column; justify-content: end; height: 200px; width: 60px; position: relative;">
                                        @{
                                            var neutralPercent = GetPercentage(DashboardData.CsatBreakdown.Neutral, DashboardData.CsatBreakdown.Positive + DashboardData.CsatBreakdown.Neutral + DashboardData.CsatBreakdown.Negative);
                                            var neutralHeight = neutralPercent * 2;
                                        }
                                        <div style="background-color: var(--mud-palette-warning); width: 100%; border-radius: 4px 4px 0 0; height: @(neutralHeight)px; position: relative;">
                                            @* Value on top of bar *@
                                            <div style="position: absolute; top: -20px; left: 50%; transform: translateX(-50%); font-size: 12px; font-weight: bold; color: var(--mud-palette-warning);">
                                                @neutralPercent.ToString("F0")%
                                            </div>
                                        </div>
                                    </div>
                                    <MudText Typo="Typo.h6" Color="Color.Warning" Class="mt-2">@DashboardData.CsatBreakdown.Neutral</MudText>
                                    <MudText Typo="Typo.body2" Class="text-center">Neutral<br/>(5-6)</MudText>
                                </div>

                                @* Negative Bar *@
                                <div style="display: flex; flex-direction: column; align-items: center; height: 100%;">
                                    <div style="display: flex; flex-direction: column; justify-content: end; height: 200px; width: 60px; position: relative;">
                                        @{
                                            var negativePercent = GetPercentage(DashboardData.CsatBreakdown.Negative, DashboardData.CsatBreakdown.Positive + DashboardData.CsatBreakdown.Neutral + DashboardData.CsatBreakdown.Negative);
                                            var negativeHeight = negativePercent * 2;
                                        }
                                        <div style="background-color: var(--mud-palette-error); width: 100%; border-radius: 4px 4px 0 0; height: @(negativeHeight)px; position: relative;">
                                            @* Value on top of bar *@
                                            <div style="position: absolute; top: -20px; left: 50%; transform: translateX(-50%); font-size: 12px; font-weight: bold; color: var(--mud-palette-error);">
                                                @negativePercent.ToString("F0")%
                                            </div>
                                        </div>
                                    </div>
                                    <MudText Typo="Typo.h6" Color="Color.Error" Class="mt-2">@DashboardData.CsatBreakdown.Negative</MudText>
                                    <MudText Typo="Typo.body2" Class="text-center">Negative<br/>(1-4)</MudText>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div style="height: 100%; display: flex; align-items: center; justify-content: center;">
                                <MudText Typo="Typo.body1" Color="Color.Secondary">No CSAT data available</MudText>
                            </div>
                        }
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        @* Python Lines 1254-1261: NPS Breakdown Chart *@
        <MudItem xs="12" md="6">
            <MudCard Class="shadow">
                @* Python Line 1256: dbc.CardHeader(html.H5("NPS Breakdown")) *@
                <MudCardHeader>
                    <MudText Typo="Typo.h6">NPS Breakdown</MudText>
                </MudCardHeader>
                <MudCardContent>
                    @* Python Line 1258: dcc.Graph(id="nps-breakdown") - Converting to pie chart *@
                    <div id="nps-breakdown" style="height: 300px; display: flex; align-items: center; justify-content: center;">
                        @if (DashboardData?.NpsBreakdown != null)
                        {
                            <MudStack AlignItems="AlignItems.Center" Spacing="4">
                                @* Simple Pie Chart using CSS *@
                                <div style="position: relative; width: 150px; height: 150px;">
                                    @* Background circle *@
                                    <div style="width: 150px; height: 150px; border-radius: 50%; background: #f44336; position: absolute;"></div>
                                    
                                    @{
                                        var total = DashboardData.NpsBreakdown.Promoters + DashboardData.NpsBreakdown.Passives + DashboardData.NpsBreakdown.Detractors;
                                        var promoterPercent = total > 0 ? (double)DashboardData.NpsBreakdown.Promoters / total * 100 : 0;
                                        var passivePercent = total > 0 ? (double)DashboardData.NpsBreakdown.Passives / total * 100 : 0;
                                    }
                                    
                                    @* Promoters segment *@
                                    @if (promoterPercent > 0)
                                    {
                                        <div style="width: 150px; height: 150px; border-radius: 50%; background: conic-gradient(#4caf50 0% @(promoterPercent)%, transparent @(promoterPercent)%); position: absolute;"></div>
                                    }
                                    
                                    @* Passives segment *@
                                    @if (passivePercent > 0)
                                    {
                                        <div style="width: 150px; height: 150px; border-radius: 50%; background: conic-gradient(transparent 0% @(promoterPercent)%, #ff9800 @(promoterPercent)% @(promoterPercent + passivePercent)%, transparent @(promoterPercent + passivePercent)%); position: absolute;"></div>
                                    }
                                    
                                    @* Center circle with NPS score *@
                                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 80px; height: 80px; background: white; border-radius: 50%; display: flex; flex-direction: column; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                        <MudText Typo="Typo.caption" Class="font-weight-bold">NPS</MudText>
                                        <MudText Typo="Typo.h6" Color="@GetNpsColor()" Class="font-weight-bold">@DashboardData.NpsScore.ToString("F0")</MudText>
                                    </div>
                                </div>
                                
                                @* Legend *@
                                <MudStack Spacing="2" AlignItems="AlignItems.Center">
                                    <MudStack Row Spacing="3" AlignItems="AlignItems.Center">
                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                            <div style="width: 12px; height: 12px; background-color: #4caf50; border-radius: 2px;"></div>
                                            <MudText Typo="Typo.body2">Promoters: @DashboardData.NpsBreakdown.Promoters (@promoterPercent.ToString("F0")%)</MudText>
                                        </MudStack>
                                    </MudStack>
                                    <MudStack Row Spacing="3" AlignItems="AlignItems.Center">
                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                            <div style="width: 12px; height: 12px; background-color: #ff9800; border-radius: 2px;"></div>
                                            <MudText Typo="Typo.body2">Passives: @DashboardData.NpsBreakdown.Passives (@passivePercent.ToString("F0")%)</MudText>
                                        </MudStack>
                                    </MudStack>
                                    <MudStack Row Spacing="3" AlignItems="AlignItems.Center">
                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                            <div style="width: 12px; height: 12px; background-color: #f44336; border-radius: 2px;"></div>
                                            @{
                                                var detractorPercent = total > 0 ? (double)DashboardData.NpsBreakdown.Detractors / total * 100 : 0;
                                            }
                                            <MudText Typo="Typo.body2">Detractors: @DashboardData.NpsBreakdown.Detractors (@detractorPercent.ToString("F0")%)</MudText>
                                        </MudStack>
                                    </MudStack>
                                </MudStack>
                            </MudStack>
                        }
                        else
                        {
                            <MudText Typo="Typo.body1" Color="Color.Secondary">No NPS data available</MudText>
                        }
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    @* Python Lines 1264-1282: Third Row - Sentiment Analysis and Key Themes Charts *@
    <MudGrid Class="mb-4">
        @* Python Lines 1265-1272: Sentiment Analysis Chart *@
        <MudItem xs="12" md="6">
            <MudCard Class="shadow">
                @* Python Line 1267: dbc.CardHeader(html.H5("Sentiment Analysis")) *@
                <MudCardHeader>
                    <MudText Typo="Typo.h6">Sentiment Analysis</MudText>
                </MudCardHeader>
                <MudCardContent>
                    @* Python Line 1269: dcc.Graph(id="sentiment-analysis") - Converting to vertical bar chart *@
                    <div id="sentiment-analysis" style="height: 300px; padding: 20px;">
                        @if (DashboardData?.SentimentCounts?.Any() == true)
                        {
                            <div style="height: 100%; display: flex; align-items: end; justify-content: space-around;">
                                @{
                                    var sentimentList = DashboardData.SentimentCounts.ToList();
                                    var colors = new[] { "var(--mud-palette-success)", "var(--mud-palette-warning)", "var(--mud-palette-error)" };
                                    var mudColors = new[] { Color.Success, Color.Warning, Color.Error };
                                }
                                @for (int i = 0; i < sentimentList.Count; i++)
                                {
                                    var sentiment = sentimentList[i];
                                    var percentage = GetPercentage(sentiment.Value, DashboardData.SentimentCounts.Values.Sum());
                                    var colorIndex = i % colors.Length;

                                    <div style="display: flex; flex-direction: column; align-items: center; height: 100%;">
                                        <div style="display: flex; flex-direction: column; justify-content: end; height: 200px; width: 60px;">
                                            <div style="background-color: @colors[colorIndex]; width: 100%; border-radius: 4px 4px 0 0; height: @(percentage * 2)px;">
                                            </div>
                                        </div>
                                        <MudText Typo="Typo.h6" Color="@mudColors[colorIndex]" Class="mt-2">@sentiment.Value</MudText>
                                        <MudText Typo="Typo.body2" Class="text-center">
                                            @GetSentimentIcon(sentiment.Key)<br/>@sentiment.Key
                                        </MudText>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div style="height: 100%; display: flex; align-items: center; justify-content: center;">
                                <MudText Typo="Typo.body1" Color="Color.Secondary">No sentiment data available</MudText>
                            </div>
                        }
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        @* Python Lines 1274-1281: Key Themes Chart *@
        <MudItem xs="12" md="6">
            <MudCard Class="shadow">
                @* Python Line 1276: dbc.CardHeader(html.H5("Key Themes in Comments")) *@
                <MudCardHeader>
                    <MudText Typo="Typo.h6">Key Themes in Comments</MudText>
                </MudCardHeader>
                <MudCardContent>
                    @* Python Line 1278: dcc.Graph(id="comment-themes") - Converting to pie chart *@
                    <div id="comment-themes" style="height: 300px; display: flex; align-items: center; justify-content: center;">
                        @if (DashboardData?.ThemeCounts?.Any() == true)
                        {
                            <MudStack AlignItems="AlignItems.Center" Spacing="4">
                                @* Simple Pie Chart for Themes *@
                                @{
                                    var topThemes = DashboardData.ThemeCounts.Take(4).ToList();
                                    var totalThemes = topThemes.Sum(t => t.Value);
                                    var colors = new[] { "#2196f3", "#4caf50", "#ff9800", "#9c27b0" };
                                }

                                <div style="position: relative; width: 150px; height: 150px;">
                                    @* Background circle *@
                                    <div style="width: 150px; height: 150px; border-radius: 50%; background: #e0e0e0; position: absolute;"></div>

                                    @{
                                        var currentPercent = 0.0;
                                    }

                                    @for (int i = 0; i < topThemes.Count && i < 4; i++)
                                    {
                                        var theme = topThemes[i];
                                        var percent = totalThemes > 0 ? (double)theme.Value / totalThemes * 100 : 0;
                                        var nextPercent = currentPercent + percent;

                                        <div style="width: 150px; height: 150px; border-radius: 50%; background: conic-gradient(transparent 0% @(currentPercent)%, @colors[i] @(currentPercent)% @(nextPercent)%, transparent @(nextPercent)%); position: absolute;"></div>

                                        currentPercent = nextPercent;
                                    }

                                    @* Center circle with total *@
                                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 80px; height: 80px; background: white; border-radius: 50%; display: flex; flex-direction: column; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                        <MudText Typo="Typo.caption" Class="font-weight-bold">Themes</MudText>
                                        <MudText Typo="Typo.h6" Color="Color.Primary" Class="font-weight-bold">@totalThemes</MudText>
                                    </div>
                                </div>

                                @* Legend *@
                                <MudStack Spacing="1" AlignItems="AlignItems.Center">
                                    @for (int i = 0; i < topThemes.Count && i < 4; i++)
                                    {
                                        var theme = topThemes[i];
                                        var color = colors[i];
                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                            <div style="width: 12px; height: 12px; background-color: @color; border-radius: 2px;"></div>
                                            <MudText Typo="Typo.body2" Style="min-width: 80px;">@theme.Key:</MudText>
                                            <MudText Typo="Typo.body2" Class="font-weight-bold">@theme.Value</MudText>
                                        </MudStack>
                                    }
                                </MudStack>
                            </MudStack>
                        }
                        else
                        {
                            <MudText Typo="Typo.body1" Color="Color.Secondary">No theme data available</MudText>
                        }
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    @* Python Lines 1284-1400+: Feedback Comments Section - EXACT CONVERSION *@
    <MudGrid Class="mb-4">
        <MudItem xs="12">
            <MudCard Class="shadow">
                @* Python Line 1288: html.H5("Feedback Comments", className="d-inline mb-3") - Title at top *@
                <MudCardHeader>
                    <MudText Typo="Typo.h6">Feedback Comments</MudText>
                </MudCardHeader>

                <MudCardContent>
                    @* Python Lines 1289-1330: Filter Controls Row *@
                    <MudGrid Class="mt-2">
                        @* Python Lines 1290-1305: Date Range Filter *@
                        <MudItem xs="12" sm="6">
                            @* Python Line 1291: html.Label("Date Range:", className="mt-2") *@
                            <MudText Typo="Typo.body2" Class="mt-2">Date Range:</MudText>
                            <MudStack Row Spacing="2" Class="mt-1">
                                @* Python Lines 1294-1302: DatePickerRange converted to MudDatePicker *@
                                <MudDatePicker @bind-Date="StartDate"
                                             Label="Start Date"
                                             Placeholder="Start Date"
                                             Clearable="true" />
                                <MudDatePicker @bind-Date="EndDate"
                                             Label="End Date"
                                             Placeholder="End Date"
                                             Clearable="true" />
                            </MudStack>
                        </MudItem>

                        @* Python Lines 1307-1316: Theme Filter *@
                        <MudItem xs="12" sm="3">
                            @* Python Line 1308: html.Label("Filter by Theme:", className="mt-2") *@
                            <MudText Typo="Typo.body2" Class="mt-2">Filter by Theme:</MudText>
                            @* Python Lines 1309-1315: Dropdown converted to MudSelect *@
                            <MudSelect T="string" @bind-Value="SelectedTheme"
                                     Label="Theme"
                                     Variant="Variant.Outlined"
                                     Clearable="false"
                                     Class="mt-1">
                                <MudSelectItem Value="@("all")">All Themes</MudSelectItem>
                                @if (ThemeOptions != null)
                                {
                                    @foreach (var theme in ThemeOptions)
                                    {
                                        <MudSelectItem Value="@theme">@theme</MudSelectItem>
                                    }
                                }
                            </MudSelect>
                        </MudItem>

                        @* Python Lines 1318-1329: Comments Count Input - EXACT CONVERSION *@
                        <MudItem xs="12" sm="3" Class="text-end">
                            @* Python Line 1319: html.Label("# Comments:", className="mt-2 d-block text-end") *@
                            <MudText Typo="Typo.body2" Class="mt-2 d-block text-end"># Comments:</MudText>
                            @* Python Lines 1320-1328: dbc.Input converted to MudNumericField *@
                            <MudNumericField @bind-Value="CommentsCount"
                                           id="comments-count-input"
                                           Label="Count"
                                           Variant="Variant.Outlined"
                                           Min="5"
                                           Max="1000"
                                           Step="5"
                                           Class="mt-1 float-end"
                                           Style="width: 80px;" />
                        </MudItem>
                    </MudGrid>

                    @* Python Lines 1332-1350: Second Filter Row *@
                    <MudGrid Class="mt-2">
                        @* Python Lines 1333-1340: Starred Comments Checkbox *@
                        <MudItem xs="12" sm="6">
                            @* Python Lines 1334-1339: Checkbox converted to MudCheckBox *@
                            <MudCheckBox T="bool" @bind-Value="ShowStarredOnly"
                                       Label="Show Starred Comments Only"
                                       Class="mt-2" />
                        </MudItem>

                        @* Python Lines 1341-1349: Apply Filters Button *@
                        <MudItem xs="12" sm="6" Class="text-end">
                            @* Python Lines 1342-1348: Button converted to MudButton *@
                            <MudButton Variant="Variant.Filled"
                                     Color="Color.Primary"
                                     Size="Size.Small"
                                     OnClick="ApplyFilters"
                                     Class="mt-2 float-end">
                                Apply Filters
                            </MudButton>
                        </MudItem>
                    </MudGrid>

                    @* Python Lines 1352-1400: Data Table Section *@
                    @* Python Lines 1353-1387: dash_table.DataTable converted to MudTable with Pagination *@
                    @if (FilteredComments?.Any() == true)
                    {
                        <MudTable T="FeedbackComment"
                                Items="@GetPagedComments()"
                                Hover="true"
                                Striped="true"
                                Dense="true"
                                MultiSelection="false"
                                @bind-SelectedItem="SelectedComment"
                                RowClassFunc="@SelectedRowClassFunc">
                            <HeaderContent>
                                @* Python Line 1356: {"name": "Date", "id": "date"} *@
                                <MudTh>Date</MudTh>
                                @* Python Line 1357: {"name": "CSAT", "id": "csat"} *@
                                <MudTh>CSAT</MudTh>
                                @* Python Line 1358: {"name": "NPS", "id": "nps"} *@
                                <MudTh>NPS</MudTh>
                                @* Python Line 1359: {"name": "Comment", "id": "comments"} *@
                                <MudTh>Comment</MudTh>
                                @* Python Line 1360: {"name": "Sentiment", "id": "sentiment"} *@
                                <MudTh>Sentiment</MudTh>
                                @* Python Line 1361: {"name": "Themes", "id": "themes"} *@
                                <MudTh>Themes</MudTh>
                                @* Python Line 1362: {"name": "Actions", "id": "actions", "presentation": "markdown"} *@
                                <MudTh>Actions</MudTh>
                            </HeaderContent>
                            <RowTemplate>
                                <MudTd DataLabel="Date">@context.Date.ToString("yyyy-MM-dd")</MudTd>
                                <MudTd DataLabel="CSAT">@context.CsatScore</MudTd>
                                <MudTd DataLabel="NPS">@context.NpsScore</MudTd>
                                <MudTd DataLabel="Comment" Style="max-width: 300px;">
                                    <MudText Typo="Typo.body2" Style="overflow: hidden; text-overflow: ellipsis;">
                                        @context.Comment
                                    </MudText>
                                </MudTd>
                                <MudTd DataLabel="Sentiment">
                                    <MudChip T="string" Color="@GetSentimentColor(context.Sentiment)" Size="Size.Small">
                                        @GetSentimentIcon(context.Sentiment) @context.Sentiment
                                    </MudChip>
                                </MudTd>
                                <MudTd DataLabel="Themes">@context.Theme</MudTd>
                                <MudTd DataLabel="Actions">
                                    @* Python Lines 1677-1723: Three action buttons - EXACT CONVERSION *@
                                    <div style="display:flex; align-items:center; gap:5px;">
                                        @* Python Lines 1678-1681: Star/Unstar button *@
                                        <MudIconButton Icon="@(context.Starred ? Icons.Material.Filled.Star : Icons.Material.Outlined.StarBorder)"
                                                     Color="@(context.Starred ? Color.Warning : Color.Default)"
                                                     Size="Size.Small"
                                                     OnClick="@(() => ToggleStarComment(context.SessionId))" />

                                        @* Python Lines 1690-1694: Contact Info button (📞) - only if contact exists *@
                                        @if (!string.IsNullOrEmpty(context.Contact) && context.Contact != "nan" && context.Contact != "None")
                                        {
                                            <MudIconButton Icon="Icons.Material.Filled.Phone"
                                                         Color="Color.Info"
                                                         Size="Size.Small"
                                                         OnClick="@(() => ShowContactInfo(context))" />
                                        }

                                        @* Python Lines 1714-1717: Share button (🔗) *@
                                        <MudIconButton Icon="Icons.Material.Filled.Share"
                                                     Color="Color.Primary"
                                                     Variant="Variant.Outlined"
                                                     Size="Size.Small"
                                                     OnClick="@(() => ShareComment(context))" />
                                    </div>
                                </MudTd>
                            </RowTemplate>
                        </MudTable>

                        @* Pagination Controls *@
                        <div class="d-flex justify-space-between align-center mt-4">
                            <MudText Typo="Typo.body2" Color="Color.Secondary">
                                Showing @(CurrentPage * PageSize + 1) to @(Math.Min((CurrentPage + 1) * PageSize, TotalItems)) of @TotalItems comments
                            </MudText>

                            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                <MudIconButton Icon="Icons.Material.Filled.FirstPage"
                                             OnClick="() => GoToPage(0)"
                                             Disabled="@(CurrentPage == 0)"
                                             Size="Size.Small" />
                                <MudIconButton Icon="Icons.Material.Filled.ChevronLeft"
                                             OnClick="() => GoToPage(CurrentPage - 1)"
                                             Disabled="@(CurrentPage == 0)"
                                             Size="Size.Small" />

                                <MudText Typo="Typo.body2">
                                    Page @(CurrentPage + 1) of @(TotalPages == 0 ? 1 : TotalPages)
                                </MudText>

                                <MudIconButton Icon="Icons.Material.Filled.ChevronRight"
                                             OnClick="() => GoToPage(CurrentPage + 1)"
                                             Disabled="@(CurrentPage >= TotalPages - 1)"
                                             Size="Size.Small" />
                                <MudIconButton Icon="Icons.Material.Filled.LastPage"
                                             OnClick="() => GoToPage(TotalPages - 1)"
                                             Disabled="@(CurrentPage >= TotalPages - 1)"
                                             Size="Size.Small" />
                            </MudStack>
                        </div>
                    }
                    else
                    {
                        <MudAlert Severity="Severity.Info">No comments available</MudAlert>
                    }
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    @* Action Recommendations Section - as shown in screenshot *@
    <MudGrid Class="mb-4">
        <MudItem xs="12">
            <MudCard Class="shadow">
                <MudCardHeader>
                    <MudText Typo="Typo.h6">Action Recommendations</MudText>
                </MudCardHeader>
                <MudCardContent>
                    <MudButton Variant="Variant.Filled"
                             Color="Color.Primary"
                             OnClick="GetRecommendations"
                             Class="mb-4">
                        Get Recommendations
                    </MudButton>

                    @if (ShowRecommendations)
                    {
                        <MudStack Spacing="4">
                            <div>
                                <MudText Typo="Typo.h6" Class="mb-2">Strengths</MudText>
                                <MudList T="string">
                                    @foreach (var strength in Strengths)
                                    {
                                        <MudListItem T="string">
                                            <MudText Typo="Typo.body2">• @strength</MudText>
                                        </MudListItem>
                                    }
                                </MudList>
                            </div>

                            <div>
                                <MudText Typo="Typo.h6" Class="mb-2">Areas for Improvement</MudText>
                                <MudList T="string">
                                    @foreach (var improvement in AreasForImprovement)
                                    {
                                        <MudListItem T="string">
                                            <MudText Typo="Typo.body2">• @improvement</MudText>
                                        </MudListItem>
                                    }
                                </MudList>
                            </div>
                        </MudStack>
                    }
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    @* Python Lines 1408-1420: Comment Action Modal - EXACT CONVERSION *@
    <MudDialog @bind-IsVisible="IsCommentActionModalOpen" Options="@(new DialogOptions { CloseButton = true, MaxWidth = MaxWidth.Small })">
        <TitleContent>
            <MudText Typo="Typo.h6">Comment Action</MudText>
        </TitleContent>
        <DialogContent>
            @* Python Line: dbc.ModalBody(id="comment-action-modal-body") *@
            <div id="comment-action-modal-body">
                @if (SelectedComment != null)
                {
                    <MudStack Spacing="3">
                        <MudText Typo="Typo.body1"><strong>Comment:</strong> @SelectedComment.Comment</MudText>
                        <MudText Typo="Typo.body2"><strong>Date:</strong> @SelectedComment.Date.ToString("yyyy-MM-dd")</MudText>
                        <MudText Typo="Typo.body2"><strong>CSAT:</strong> @SelectedComment.CsatScore</MudText>
                        <MudText Typo="Typo.body2"><strong>NPS:</strong> @SelectedComment.NpsScore</MudText>
                        <MudText Typo="Typo.body2"><strong>Sentiment:</strong> @SelectedComment.Sentiment</MudText>
                        <MudText Typo="Typo.body2"><strong>Theme:</strong> @SelectedComment.Theme</MudText>

                        <MudDivider />

                        <MudText Typo="Typo.subtitle2">Available Actions:</MudText>
                        <MudStack Row Spacing="2">
                            <MudButton Variant="Variant.Outlined"
                                     Color="@(SelectedComment.Starred ? Color.Warning : Color.Default)"
                                     StartIcon="@(SelectedComment.Starred ? Icons.Material.Filled.Star : Icons.Material.Outlined.StarBorder)"
                                     OnClick="@(() => SetCommentAction(SelectedComment.Starred ? "unstar" : "star"))">
                                @(SelectedComment.Starred ? "Unstar" : "Star") Comment
                            </MudButton>
                            <MudButton Variant="Variant.Outlined"
                                     Color="Color.Info"
                                     StartIcon="Icons.Material.Outlined.Flag"
                                     OnClick="@(() => SetCommentAction("flag"))">
                                Flag for Review
                            </MudButton>
                        </MudStack>
                    </MudStack>
                }
            </div>
        </DialogContent>
        <DialogActions>
            @* Python Lines: dbc.Button("Cancel", id="comment-action-cancel", className="me-2") *@
            <MudButton id="comment-action-cancel"
                     OnClick="CancelCommentAction"
                     Class="me-2">
                Cancel
            </MudButton>
            @* Python Lines: dbc.Button("Confirm", id="comment-action-confirm", color="primary") *@
            <MudButton id="comment-action-confirm"
                     Variant="Variant.Filled"
                     Color="Color.Primary"
                     OnClick="ConfirmCommentAction"
                     Disabled="@string.IsNullOrEmpty(PendingCommentAction)">
                Confirm
            </MudButton>
        </DialogActions>
    </MudDialog>

</MudContainer>

@* Python Lines 1415-1421: Footer - EXACT CONVERSION *@
<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-4">
    <MudGrid>
        <MudItem xs="12">
            @* Python Line 1418: html.P(f"© 2025 {DASHBOARD_TITLE}", className="text-center text-muted mt-4") *@
            <MudText Typo="Typo.body2" Align="Align.Center" Color="Color.Secondary" Class="mt-4">
                © 2025 Hospital Voice of Customer Dashboard
            </MudText>
        </MudItem>
    </MudGrid>
</MudContainer>

@code {
    // Properties converted from Python class variables
    private DashboardData? DashboardData { get; set; }
    private DateTime LastUpdated { get; set; } = DateTime.Now;
    private bool IsLoading { get; set; } = true;

    // Comments filtering - converted from Python callback parameters
    private List<FeedbackComment>? FilteredComments { get; set; }
    private List<string>? ThemeOptions { get; set; }
    private DateTime? StartDate { get; set; } = DateTime.Now.AddDays(-30);
    private DateTime? EndDate { get; set; } = DateTime.Now;
    private string SelectedTheme { get; set; } = "all";
    private bool ShowStarredOnly { get; set; } = false;
    private int CommentsCount { get; set; } = 100; // Python: comments_count default value
    private FeedbackComment? SelectedComment { get; set; }

    // Pagination properties
    private int CurrentPage { get; set; } = 0; // 0-based page index
    private int PageSize { get; set; } = 10; // Initial page size of 10
    private int TotalItems => Math.Min(FilteredComments?.Count ?? 0, CommentsCount);
    private int TotalPages => (int)Math.Ceiling((double)TotalItems / PageSize);

    // Comment Action Modal properties - converted from Python modal state
    private bool IsCommentActionModalOpen { get; set; } = false;
    private string PendingCommentAction { get; set; } = string.Empty;

    // Auto-refresh timer - converted from Python dcc.Interval (5*60*1000 = 5 minutes)
    private Timer? RefreshTimer { get; set; }
    private readonly int RefreshIntervalMs = 5 * 60 * 1000; // 5 minutes

    // Action Recommendations properties
    private bool ShowRecommendations { get; set; } = false;
    private List<string> Strengths { get; set; } = new();
    private List<string> AreasForImprovement { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();

        // Initialize auto-refresh timer - converted from Python dcc.Interval
        RefreshTimer = new Timer(async _ => await RefreshDashboardData(), null, RefreshIntervalMs, RefreshIntervalMs);
    }

    public void Dispose()
    {
        RefreshTimer?.Dispose();
    }

    // Converted from Python: DashboardApp.__init__ and data loading
    private async Task LoadDashboardData()
    {
        try
        {
            IsLoading = true;
            StateHasChanged();

            // For demonstration purposes, create sample data
            // In production, this would be: DashboardData = await DataAnalyzer.GetDashboardDataAsync();
            DashboardData = CreateSampleDashboardData();
            LastUpdated = DateTime.Now;

            // Initialize filtered comments - converted from Python callback initialization
            if (DashboardData?.Comments != null)
            {
                ThemeOptions = DashboardData.Comments.Select(c => c.Theme).Distinct().ToList();

                // Apply initial filters to set up FilteredComments and SelectedComment
                await ApplyFilters();
            }
        }
        catch (Exception ex)
        {
            // Python equivalent: logger.error(f"Error loading dashboard data: {ex}")
            Console.WriteLine($"Error loading dashboard data: {ex}");
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    // Sample data for demonstration - converted from Python test data
    private DashboardData CreateSampleDashboardData()
    {
        var random = new Random();
        var comments = new List<FeedbackComment>();

        // Sample themes from Python healthcare industry
        var themes = new[] { "Staff Attitude", "Wait Time", "Cleanliness", "Communication", "Treatment Quality", "Facilities" };
        var sentiments = new[] { "Positive", "Neutral", "Negative" };

        // Generate 150 sample comments
        for (int i = 1; i <= 150; i++)
        {
            var sentiment = sentiments[random.Next(sentiments.Length)];
            var theme = themes[random.Next(themes.Length)];
            var csatScore = sentiment == "Positive" ? random.Next(7, 11) :
                           sentiment == "Negative" ? random.Next(1, 5) : random.Next(5, 7);
            var npsScore = sentiment == "Positive" ? random.Next(9, 11) :
                          sentiment == "Negative" ? random.Next(0, 7) : random.Next(7, 9);

            comments.Add(new FeedbackComment
            {
                SessionId = $"SID_{i:D4}",
                Date = DateTime.Now.AddDays(-random.Next(0, 30)),
                CsatScore = csatScore,
                NpsScore = npsScore,
                Comment = GetSampleComment(theme, sentiment),
                Sentiment = sentiment,
                Theme = theme,
                Starred = random.Next(1, 10) == 1, // 10% chance of being starred
                Contact = random.Next(1, 4) == 1 ? $"555-{random.Next(1000, 9999)}" : string.Empty // 25% have contact info
            });
        }

        // Calculate metrics from sample data
        var csatAverage = comments.Where(c => c.CsatScore > 0).Average(c => c.CsatScore);
        var calculatedNpsScore = CalculateNPS(comments);

        return new DashboardData
        {
            ActiveIndustry = "Healthcare",
            CsatAverage = csatAverage,
            CsatBreakdown = new CsatBreakdown
            {
                Positive = comments.Count(c => c.CsatScore >= 7),
                Neutral = comments.Count(c => c.CsatScore >= 5 && c.CsatScore < 7),
                Negative = comments.Count(c => c.CsatScore < 5)
            },
            NpsScore = calculatedNpsScore,
            NpsBreakdown = new NpsBreakdown
            {
                Promoters = comments.Count(c => c.NpsScore >= 9),
                Passives = comments.Count(c => c.NpsScore >= 7 && c.NpsScore < 9),
                Detractors = comments.Count(c => c.NpsScore < 7)
            },
            SentimentCounts = comments.GroupBy(c => c.Sentiment).ToDictionary(g => g.Key, g => g.Count()),
            ThemeCounts = comments.GroupBy(c => c.Theme).ToDictionary(g => g.Key, g => g.Count()),
            AvailableThemes = themes.ToList(),
            Comments = comments
        };
    }

    private string GetSampleComment(string theme, string sentiment)
    {
        var positiveComments = new Dictionary<string, string[]>
        {
            ["Staff Attitude"] = new[] { "The nurses were incredibly kind and caring.", "Doctor was very professional and helpful.", "Staff went above and beyond to help." },
            ["Wait Time"] = new[] { "Very quick service, no waiting.", "Appointment was on time.", "Fast and efficient process." },
            ["Cleanliness"] = new[] { "Hospital was spotless and clean.", "Very hygienic environment.", "Everything was sanitized properly." },
            ["Communication"] = new[] { "Doctor explained everything clearly.", "Staff communicated well.", "Information was clear and helpful." },
            ["Treatment Quality"] = new[] { "Excellent medical care received.", "Treatment was very effective.", "High quality healthcare service." },
            ["Facilities"] = new[] { "Modern and well-equipped facility.", "Comfortable rooms and amenities.", "Great parking and accessibility." }
        };

        var negativeComments = new Dictionary<string, string[]>
        {
            ["Staff Attitude"] = new[] { "Staff was rude and unhelpful.", "Nurses seemed uninterested.", "Poor customer service attitude." },
            ["Wait Time"] = new[] { "Waited for hours without updates.", "Very long delays.", "Appointment was delayed significantly." },
            ["Cleanliness"] = new[] { "Facility was not clean.", "Hygiene standards were poor.", "Dirty and unkempt areas." },
            ["Communication"] = new[] { "Doctor didn't explain anything.", "Poor communication from staff.", "Information was confusing." },
            ["Treatment Quality"] = new[] { "Treatment was ineffective.", "Poor quality of care.", "Medical service was substandard." },
            ["Facilities"] = new[] { "Old and outdated equipment.", "Uncomfortable facilities.", "Poor parking situation." }
        };

        var neutralComments = new Dictionary<string, string[]>
        {
            ["Staff Attitude"] = new[] { "Staff was okay, nothing special.", "Average service from nurses.", "Staff was professional but distant." },
            ["Wait Time"] = new[] { "Wait time was reasonable.", "Some delay but manageable.", "Average waiting period." },
            ["Cleanliness"] = new[] { "Facility was adequately clean.", "Standard hygiene levels.", "Clean enough but could be better." },
            ["Communication"] = new[] { "Communication was adequate.", "Information was provided as needed.", "Standard level of explanation." },
            ["Treatment Quality"] = new[] { "Treatment was satisfactory.", "Standard quality of care.", "Medical service was adequate." },
            ["Facilities"] = new[] { "Facilities were adequate.", "Standard hospital amenities.", "Acceptable but not impressive." }
        };

        var random = new Random();
        return sentiment switch
        {
            "Positive" => positiveComments[theme][random.Next(positiveComments[theme].Length)],
            "Negative" => negativeComments[theme][random.Next(negativeComments[theme].Length)],
            _ => neutralComments[theme][random.Next(neutralComments[theme].Length)]
        };
    }

    private double CalculateNPS(List<FeedbackComment> comments)
    {
        if (!comments.Any()) return 0;

        var promoters = comments.Count(c => c.NpsScore >= 9);
        var detractors = comments.Count(c => c.NpsScore < 7);
        var total = comments.Count;

        return ((double)(promoters - detractors) / total) * 100;
    }

    private double GetPercentage(int value, int total)
    {
        return total > 0 ? (double)value / total * 100 : 0;
    }

    private Color GetNpsColor()
    {
        if (DashboardData?.NpsScore >= 50) return Color.Success;
        if (DashboardData?.NpsScore >= 0) return Color.Warning;
        return Color.Error;
    }

    // Auto-refresh functionality - converted from Python dcc.Interval callback
    private async Task RefreshDashboardData()
    {
        try
        {
            // Refresh data in background without showing loading state
            var newData = CreateSampleDashboardData(); // In production: await DataAnalyzer.GetDashboardDataAsync();
            DashboardData = newData;
            LastUpdated = DateTime.Now;

            // Reapply filters to update filtered comments
            if (DashboardData?.Comments != null)
            {
                ThemeOptions = DashboardData.Comments.Select(c => c.Theme).Distinct().ToList();
                await ApplyFilters();
            }

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error during auto-refresh: {ex}");
        }
    }

    // Comment Action Modal methods - converted from Python modal callbacks
    private void SetCommentAction(string action)
    {
        PendingCommentAction = action;
        StateHasChanged();
    }

    private void CancelCommentAction()
    {
        IsCommentActionModalOpen = false;
        PendingCommentAction = string.Empty;
        StateHasChanged();
    }

    private void ConfirmCommentAction()
    {
        if (SelectedComment != null && !string.IsNullOrEmpty(PendingCommentAction))
        {
            switch (PendingCommentAction.ToLower())
            {
                case "star":
                    SelectedComment.Starred = true;
                    break;
                case "unstar":
                    SelectedComment.Starred = false;
                    break;
                case "flag":
                    // In a real implementation, this would flag the comment for review
                    Console.WriteLine($"Comment {SelectedComment.SessionId} flagged for review");
                    break;
            }

            // Close modal and reset state
            IsCommentActionModalOpen = false;
            PendingCommentAction = string.Empty;
            StateHasChanged();
        }
    }



    // Pagination helper methods - CommentsCount limits total, PageSize limits per page
    private IEnumerable<FeedbackComment> GetPagedComments()
    {
        if (FilteredComments == null) return Enumerable.Empty<FeedbackComment>();

        // CommentsCount defines how many comments can be shown in table (5-1000)
        // PageSize defines how many comments per page
        var limitedComments = FilteredComments.Take(CommentsCount);
        return limitedComments.Skip(CurrentPage * PageSize).Take(PageSize);
    }

    private void GoToPage(int page)
    {
        if (page >= 0 && page < TotalPages)
        {
            CurrentPage = page;
            // Select first item on new page
            SelectedComment = GetPagedComments().FirstOrDefault();
            StateHasChanged();
        }
    }

    private async Task OnPageSizeChanged(int newPageSize)
    {
        PageSize = newPageSize;
        CurrentPage = 0; // Reset to first page
        SelectedComment = GetPagedComments().FirstOrDefault();
        StateHasChanged();
        await Task.CompletedTask;
    }

    // CONVERTED FROM PYTHON: Action button methods (lines 1678-1717)

    // Python lines 1678-1681: Star/Unstar functionality
    private void ToggleStarComment(string sessionId)
    {
        try
        {
            var comment = FilteredComments?.FirstOrDefault(c => c.SessionId == sessionId);
            if (comment != null)
            {
                comment.Starred = !comment.Starred;
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error toggling star: {ex}");
        }
    }

    // Python lines 1690-1694: Contact Info functionality
    private void ShowContactInfo(FeedbackComment comment)
    {
        try
        {
            // Python equivalent: contact-info-btn with tooltip
            var message = $"Contact: {comment.Contact}\nDate: {comment.Date:yyyy-MM-dd}";
            Console.WriteLine($"Contact Info: {message}");
            // In a real implementation, this could show contact details
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error showing contact info: {ex}");
        }
    }

    // Python lines 1714-1717: Share functionality
    private void ShareComment(FeedbackComment comment)
    {
        try
        {
            // Python equivalent: share-btn mailto link
            var subject = Uri.EscapeDataString($"Feedback from {comment.Date:yyyy-MM-dd}");
            var body = Uri.EscapeDataString(
                $"CSAT: {comment.CsatScore}\n" +
                $"NPS: {comment.NpsScore}\n" +
                $"Contact: {comment.Contact}\n" +
                $"Date: {comment.Date:yyyy-MM-dd}\n\n" +
                $"Comment:\n{comment.Comment}"
            );
            var mailtoUrl = $"mailto:?subject={subject}&body={body}";
            Console.WriteLine($"Share URL: {mailtoUrl}");
            // In a real implementation, this would open the default email client
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error sharing comment: {ex}");
        }
    }

    // Action Recommendations method
    private void GetRecommendations()
    {
        try
        {
            // Generate sample recommendations based on current data
            Strengths = new List<string>
            {
                "High customer satisfaction in treatment quality",
                "Positive feedback on staff professionalism",
                "Effective communication with patients"
            };

            AreasForImprovement = new List<string>
            {
                "Reduce wait times during peak hours",
                "Improve facility cleanliness standards",
                "Enhance appointment scheduling system"
            };

            ShowRecommendations = true;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error generating recommendations: {ex}");
        }
    }

    // EXACT CONVERSION FROM PYTHON: update_comments_table function (lines 1653-1732)
    private async Task ApplyFilters()
    {
        if (DashboardData?.Comments == null) return;

        // Python lines 1658-1661: Validate comments_count
        if (CommentsCount < 5)
        {
            CommentsCount = 5;
        }
        else if (CommentsCount > 1000)
        {
            CommentsCount = 1000;
        }

        // Python lines 1663-1669: Filter comments by date range, theme, and starred status
        FilteredComments = DashboardData.Comments.Where(c =>
        {
            // Date filter
            if (StartDate.HasValue && c.Date < StartDate.Value) return false;
            if (EndDate.HasValue && c.Date > EndDate.Value) return false;

            // Theme filter
            if (SelectedTheme != "all" && c.Theme != SelectedTheme) return false;

            // Starred filter
            if (ShowStarredOnly && !c.Starred) return false;

            return true;
        }).ToList();

        // Select the first row after filtering (Python equivalent: first row selection)
        SelectedComment = FilteredComments?.FirstOrDefault();

        // Reset to first page when filters change
        CurrentPage = 0;

        StateHasChanged();
        await Task.CompletedTask;
    }



    // CONVERTED FROM PYTHON: Row selection styling
    private string SelectedRowClassFunc(FeedbackComment item, int rowNumber)
    {
        if (SelectedComment != null && SelectedComment.SessionId == item.SessionId)
        {
            return "selected-row";
        }
        return string.Empty;
    }

    // CONVERTED FROM PYTHON: Sentiment analysis helpers
    private string GetSentimentIcon(string sentiment)
    {
        return sentiment?.ToLower() switch
        {
            "positive" => "😊",
            "negative" => "😞",
            "neutral" => "😐",
            _ => "❓"
        };
    }

    private Color GetSentimentColor(string sentiment)
    {
        return sentiment?.ToLower() switch
        {
            "positive" => Color.Success,
            "negative" => Color.Error,
            "neutral" => Color.Warning,
            _ => Color.Default
        };
    }
}
