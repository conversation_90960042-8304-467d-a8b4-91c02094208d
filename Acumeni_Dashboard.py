#!/usr/bin/env python3
import os
import json
import logging
import datetime
import requests
import pandas as pd
import numpy as np
from io import StringIO
from dotenv import load_dotenv
import plotly.express as px
import plotly.graph_objects as go
import re
import urllib.parse
from dash import Dash, html, dcc, callback, Output, Input, State, dash_table, callback_context
import dash_bootstrap_components as dbc
from dash.exceptions import PreventUpdate
from apscheduler.schedulers.background import BackgroundScheduler
from textblob import TextBlob
import threading
import time
import traceback
import sys
try:
    import openai
    _OPENAI_AVAILABLE = True
except ImportError:
    _OPENAI_AVAILABLE = False
    logger.warning('openai library not installed—recommendations disabled')

def get_default_industry_themes(industry):
    """Return default themes for a specific industry."""
    industry_themes = {
        "Healthcare": {
            "Staff Attitude": ["friendly", "rude", "polite", "attitude", "helpful", "kind", "caring", "nurse", "doctor", "staff", "receptionist"],
            "Wait Time": ["wait", "delay", "quick", "prompt", "slow", "hours", "fast", "appointment", "time"],
            "Cleanliness": ["clean", "dirty", "hygiene", "sanitized", "unclean", "spotless", "sanitary", "infection"],
            "Communication": ["explain", "information", "clear", "confusing", "understood", "communication", "informed"],
            "Treatment Quality": ["treatment", "care", "procedure", "diagnosis", "medicine", "prescription", "therapy", "effective", "pain"],
            "Facilities": ["room", "bed", "equipment", "facility", "amenities", "parking", "building", "cafeteria"]
        },
        "Banking": {
            "Staff Service": ["helpful", "friendly", "professional", "staff", "teller", "advisor"],
            "Wait Time": ["queue", "wait", "time", "quick", "slow", "busy", "line"],
            "Digital Banking": ["app", "online", "mobile", "website", "internet", "technical", "login", "password"],
            "Fees & Rates": ["fees", "charges", "interest", "rate", "expensive", "cost", "price"],
            "Account Issues": ["account", "balance", "transaction", "transfer", "deposit", "withdraw"],
            "Loan Process": ["loan", "mortgage", "application", "approval", "credit", "rejected"]
        },
        "Telecom": {
            "Network": ["coverage", "signal", "reception", "internet", "voice", "5G", "4G", "connection", "buffering", "download"],
            "Customer Service": ["friendly", "rude", "polite", "attitude", "helpful", "kind", "caring"],
            "Pricing & Plans": ["expensive", "affordable", "price", "cost", "plan", "package", "value"],
            "Billing": ["bill", "payment", "charge", "topup", "airtime", "credit"],
            "Phones": ["samsung", "iPhone", "smartphone", "shop", "range", "warranty"],
            "Website & App": ["app", "website", "portal", "login", "user-friendly", "crashes"]
        },
        "Retail": {
            "Store Experience": ["layout", "store", "clean", "crowded", "atmosphere", "music"],
            "Staff Service": ["helpful", "friendly", "knowledgeable", "staff", "salesperson", "cashier"],
            "Product Range": ["selection", "variety", "range", "choice", "stock", "availability"],
            "Product Quality": ["quality", "defective", "broken", "durable", "condition"],
            "Price": ["expensive", "cheap", "affordable", "price", "cost", "value", "discount", "sale"],
            "Returns": ["return", "refund", "exchange", "policy", "warranty"]
        },
        "Airlines": {
            "Flight Experience": ["comfortable", "cramped", "seat", "legroom", "entertainment", "wifi", "food", "drinks"],
            "Staff Service": ["crew", "flight attendant", "staff", "pilot", "friendly", "helpful", "professional"],
            "Punctuality": ["delay", "on time", "late", "wait", "canceled", "rescheduled"],
            "Baggage": ["luggage", "baggage", "lost", "damaged", "claim", "allowance", "weight"],
            "Booking": ["reservation", "website", "ticket", "price", "change", "seat selection"],
            "Airport Experience": ["check-in", "security", "boarding", "gate", "lounge"]
        },
        "Hospitality": {
            "Room Quality": ["clean", "comfortable", "spacious", "bed", "bathroom", "shower", "amenities"],
            "Staff Service": ["reception", "staff", "friendly", "helpful", "concierge", "housekeeping"],
            "Food & Dining": ["breakfast", "restaurant", "food", "meal", "dinner", "quality", "service", "menu"],
            "Facilities": ["pool", "gym", "spa", "wifi", "internet", "parking", "elevator"],
            "Location": ["central", "quiet", "noisy", "convenient", "transport", "view"],
            "Value": ["price", "expensive", "worth", "value", "cost", "affordable"]
        },
        "Insurance": {
            "Claims Process": ["claim", "process", "payment", "settlement", "rejected", "approved", "delay"],
            "Customer Service": ["staff", "agent", "helpful", "responsive", "advisor", "consultant"],
            "Policy Terms": ["coverage", "policy", "terms", "clear", "understand", "exclusions", "included"],
            "Pricing": ["premium", "price", "expensive", "affordable", "quote", "increase", "discount"],
            "Communication": ["explained", "information", "updates", "letter", "email", "call"],
            "Website & App": ["website", "app", "online", "account", "portal", "access"]
        },
        "Transport": {
            "Punctuality": ["delay", "late", "on time", "waiting", "schedule", "frequency"],
            "Comfort": ["seats", "clean", "air conditioning", "heating", "crowded", "comfortable", "condition"],
            "Staff": ["driver", "conductor", "staff", "helpful", "friendly", "rude"],
            "Facilities": ["station", "stop", "shelter", "information", "toilet", "accessibility"],
            "Value": ["price", "fare", "ticket", "expensive", "cheap", "value", "cost"],
            "Safety": ["safe", "dangerous", "security", "driving", "speed", "accident"]
        },
        "Government": {
            "Service Efficiency": ["wait", "queue", "time", "quick", "slow", "process", "bureaucracy", "paperwork"],
            "Staff Attitude": ["helpful", "rude", "friendly", "polite", "competent", "knowledgeable"],
            "Information Clarity": ["clear", "confusing", "information", "instructions", "forms", "guidance"],
            "Online Services": ["website", "portal", "online", "digital", "access", "login", "account"],
            "Responsiveness": ["response", "wait", "answer", "contacted", "follow-up", "resolved"],
            "Facilities": ["building", "office", "clean", "accessible", "location", "parking"]
        }
    }

    # Return themes for specified industry or Healthcare as default
    return industry_themes.get(industry, industry_themes["Healthcare"])


logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

print("Starting Acumeni Dashboard...")
print(f"Python version: {sys.version}")

def load_environment_variables():
    """Load environment variables with dynamic reloading support"""
    try:
        print("Loading environment variables...")
        load_dotenv(override=True)

        if os.path.exists('.env_reload'):
            try:
                os.remove('.env_reload')
                logger.info("Detected environment reload signal. Reloading variables.")
                load_dotenv(override=True)
            except Exception as e:
                logger.error(f"Error handling reload signal: {e}")

        # Get active industry from environment variable, default to Healthcare
        active_industry = os.getenv("ACTIVE_INDUSTRY", "Healthcare")
        logger.info(f"Active Industry: {active_industry}")

        env_vars = {
            "HOST": os.getenv("HOST", "https://surveys.acumeni.net"),
            "USERNAME": os.getenv("USERNAME", "acumeniapi.acumeni"),
            "PASSWORD": os.getenv("PASSWORD", "Test1234!"),
            "SURVEY_CODE": os.getenv("SURVEY_CODE", "81844"),
            "DATA_REFRESH_MINUTES": int(os.getenv("DATA_REFRESH_MINUTES", "15")),
            "DASHBOARD_TITLE": os.getenv("DASHBOARD_TITLE", f"{active_industry} Voice of Customer Dashboard"),
            "DASHBOARD_SUBTITLE": os.getenv("DASHBOARD_SUBTITLE", f"Real-time analysis of {active_industry.lower()} survey responses"),
            "CSAT_QUESTION": os.getenv("CSAT_QUESTION", "Validated_Q1"),
            "NPS_QUESTION": os.getenv("NPS_QUESTION", "Validated_Q2"),
            "VERBATIM_QUESTION": os.getenv("VERBATIM_QUESTION", "Validated_Q3"),
            "PHONE_COLUMN": os.getenv("PHONE_COLUMN", "Phone_Number"),
            "ACTIVE_INDUSTRY": active_industry
        }

        # Initialize themes dictionary
        custom_themes = {}

        # Load custom themes from environment variables
        for i in range(1, 10):  # Support up to 9 theme categories
            theme_name_var = f"THEME_{i}_NAME"
            theme_keywords_var = f"THEME_{i}_KEYWORDS"

            theme_name = os.getenv(theme_name_var)
            theme_keywords = os.getenv(theme_keywords_var)

            if theme_name and theme_keywords:
                # Split comma-separated keywords and strip whitespace
                keywords_list = [kw.strip() for kw in theme_keywords.split(',')]
                custom_themes[theme_name] = keywords_list
                logger.info(f"Loaded custom theme '{theme_name}' with {len(keywords_list)} keywords")

        # If custom themes are defined, use them; otherwise use default themes for the industry
        if custom_themes:
            logger.info(f"Using {len(custom_themes)} custom themes from environment variables")
            env_vars["INDUSTRY_THEMES"] = custom_themes
        else:
            logger.info(f"Using default themes for industry: {active_industry}")
            env_vars["INDUSTRY_THEMES"] = get_default_industry_themes(active_industry)

        logger.info(f"Dashboard Title: {env_vars['DASHBOARD_TITLE']}")
        logger.info(f"Dashboard Subtitle: {env_vars['DASHBOARD_SUBTITLE']}")
        logger.info(f"Survey Code: {env_vars['SURVEY_CODE']}")
        logger.info(f"CSAT Question: {env_vars['CSAT_QUESTION']}")
        logger.info(f"NPS Question: {env_vars['NPS_QUESTION']}")
        logger.info(f"Verbatim Question: {env_vars['VERBATIM_QUESTION']}")
        logger.info(f"Phone Column: {env_vars['PHONE_COLUMN']}")

        return env_vars
    except Exception as e:
        logger.error(f"Error loading environment variables: {e}")
        logger.error(traceback.format_exc())
        return {
            "HOST": "https://surveys.acumeni.net",
            "USERNAME": "acumeniapi.acumeni",
            "PASSWORD": "Test1234!",
            "SURVEY_CODE": "81844",
            "DATA_REFRESH_MINUTES": 15,
            "DASHBOARD_TITLE": "Hospital Voice of Customer Dashboard",
            "DASHBOARD_SUBTITLE": "Real-time analysis of hospital survey responses",
            "CSAT_QUESTION": "Validated_Q1",
            "NPS_QUESTION": "Validated_Q2",
            "VERBATIM_QUESTION": "Validated_Q3",
            "PHONE_COLUMN": "Phone_Number",
            "ACTIVE_INDUSTRY": "Healthcare",
            "INDUSTRY_THEMES": get_default_industry_themes("Healthcare")
        }

try:
    print("Loading environment variables...")
    ENV = load_environment_variables()
    HOST = ENV["HOST"]
    USERNAME = ENV["USERNAME"]
    PASSWORD = ENV["PASSWORD"]
    SURVEY_CODE = ENV["SURVEY_CODE"]
    DATA_REFRESH_MINUTES = ENV["DATA_REFRESH_MINUTES"]
    DASHBOARD_TITLE = ENV["DASHBOARD_TITLE"]
    DASHBOARD_SUBTITLE = ENV["DASHBOARD_SUBTITLE"]
    CSAT_QUESTION = ENV["CSAT_QUESTION"]
    NPS_QUESTION = ENV["NPS_QUESTION"]
    VERBATIM_QUESTION = ENV["VERBATIM_QUESTION"]
    PHONE_COLUMN = ENV["PHONE_COLUMN"]

    # Configure OpenAI API key if available
    if _OPENAI_AVAILABLE:
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if openai_api_key:
            openai.api_key = openai_api_key
            logger.info("OpenAI API key configured successfully")
        else:
            logger.warning("OpenAI API key not found in environment variables")
            _OPENAI_AVAILABLE = False

    RAW_DATA_FILE = "raw_data.csv"
    PROCESSED_DATA_FILE = "processed_data.csv"
    LAST_ID_FILE = "last_id.txt"
    SENTIMENT_SCORES_FILE = "sentiment_scores.json"
    THEMES_FILE = "comment_themes.json"
    STARRED_COMMENTS_FILE = "starred_comments.json"

    LAST_UPDATE_TIME = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
except Exception as e:
    logger.error(f"Initialization error: {e}")
    logger.error(traceback.format_exc())
    raise

def analyze_sentiment(texts, batch_size=50):
    results = []
    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i:i+batch_size]
        batch_results = []
        
        for text in batch_texts:
            if not isinstance(text, str) or not text.strip():
                batch_results.append({"label": "NEUTRAL", "score": 0.5})
                continue
                
            analysis = TextBlob(text)
            polarity = analysis.sentiment.polarity
            
            if polarity > 0.1:
                batch_results.append({"label": "POSITIVE", "score": 0.5 + polarity/2})
            elif polarity < -0.1:
                batch_results.append({"label": "NEGATIVE", "score": 0.5 - polarity/2})
            else:
                batch_results.append({"label": "NEUTRAL", "score": 0.5})
        
        results.extend(batch_results)
    
    return results

class AcumeniAPI:
    def __init__(self, host, username, password):
        self.host = host
        self.username = username
        self.password = password
        self.last_id = self._get_last_id()
        
    def _get_last_id(self):
        """Retrieve the last session ID from file or start from 0"""
        try:
            if os.path.exists(LAST_ID_FILE):
                with open(LAST_ID_FILE, 'r') as f:
                    return int(f.read().strip())
            return 0
        except Exception as e:
            logger.error(f"Error reading last ID: {e}")
            return 0
            
    def _save_last_id(self, last_id):
        """Save the last session ID to file"""
        try:
            with open(LAST_ID_FILE, 'w') as f:
                f.write(str(last_id))
            logger.info(f"Successfully saved last_id: {last_id}")
        except Exception as e:
            logger.error(f"Error saving last ID: {e}")
    
    def get_survey_data(self, survey_id, status=1):
        """Fetch survey data from Acumeni API with timeout handling"""
        try:
            url = f"{self.host}/api.php"
            params = {
                "user": self.username,
                "pass": self.password,
                "cmd": "getSurveySessions",
                "surveyId": survey_id,
                "status": status,
                "startId": self.last_id
            }
            
            logger.info(f"Fetching data from Acumeni API with startId={self.last_id}")
            response = requests.get(url, params=params, timeout=(5, 30))
            
            if response.status_code == 200:
                data = response.text
                
                if not (',' in data and '\n' in data):
                    logger.warning(f"Received non-CSV response: {data[:100]}")
                    return False
                
                if data.startswith('004,Error'):
                    logger.warning(f"API returned an error: {data}")
                    return False
                
                if data and len(data) > 0:
                    df = pd.read_csv(StringIO(data))
                    
                    if not df.empty and 'ID' in df.columns:
                        max_id = df['ID'].max()
                        if max_id > self.last_id:
                            self.last_id = max_id
                            self._save_last_id(max_id)
                            logger.info(f"Updated last_id to {max_id}")
                    
                    if os.path.exists(RAW_DATA_FILE):
                        existing_df = pd.read_csv(RAW_DATA_FILE)
                        
                        if 'ID' in existing_df.columns and 'ID' in df.columns:
                            existing_ids = set(existing_df['ID'].astype(str))
                            df = df[~df['ID'].astype(str).isin(existing_ids)]
                        
                        combined_df = pd.concat([existing_df, df])
                        combined_df.to_csv(RAW_DATA_FILE, index=False)
                        logger.info(f"Appended {len(df)} new records to raw data file")
                    else:
                        df.to_csv(RAW_DATA_FILE, index=False)
                        logger.info(f"Created new raw data file with {len(df)} records")
                    
                    processed_df = self._process_data(df)
                    
                    if os.path.exists(PROCESSED_DATA_FILE):
                        existing_processed_df = pd.read_csv(PROCESSED_DATA_FILE)
                        if 'session_id' in existing_processed_df.columns and 'session_id' in processed_df.columns:
                            existing_session_ids = set(existing_processed_df['session_id'].astype(str))
                            processed_df = processed_df[~processed_df['session_id'].astype(str).isin(existing_session_ids)]
                        
                        combined_processed_df = pd.concat([existing_processed_df, processed_df])
                        combined_processed_df.to_csv(PROCESSED_DATA_FILE, index=False)
                        logger.info(f"Added {len(processed_df)} new records to processed data file")
                    else:
                        processed_df.to_csv(PROCESSED_DATA_FILE, index=False)
                        logger.info(f"Created new processed data file with {len(processed_df)} records")
                    
                    global LAST_UPDATE_TIME
                    LAST_UPDATE_TIME = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    
                    return True
                else:
                    logger.info("No new data received from API")
                    return False
            else:
                logger.error(f"API request failed with status code {response.status_code}: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            logger.error("API request timed out")
            return False
        except Exception as e:
            logger.error(f"Error fetching survey data: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def _process_data(self, df):
        """Process Acumeni API data into standardized format with dynamic question mapping"""
        try:
            processed_df = pd.DataFrame()
            
            if 'ID' in df.columns:
                processed_df['session_id'] = df['ID']
            else:
                processed_df['session_id'] = range(len(df))
            
            if 'Session_Date' in df.columns:
                processed_df['date'] = df['Session_Date']
            else:
                processed_df['date'] = pd.Timestamp.now().strftime('%Y-%m-%d')
            
            if CSAT_QUESTION in df.columns:
                processed_df['csat'] = df[CSAT_QUESTION]
            elif 'Q1' in df.columns and CSAT_QUESTION not in df.columns:
                processed_df['csat'] = df['Q1']
            
            if NPS_QUESTION in df.columns:
                processed_df['nps'] = df[NPS_QUESTION]
            elif 'Q2' in df.columns and NPS_QUESTION not in df.columns:
                processed_df['nps'] = df['Q2']
            
            if VERBATIM_QUESTION in df.columns:
                processed_df['comments'] = df[VERBATIM_QUESTION]
            elif 'Q3' in df.columns and VERBATIM_QUESTION not in df.columns:
                processed_df['comments'] = df['Q3']
            
            if PHONE_COLUMN in df.columns:
                processed_df['contact'] = df[PHONE_COLUMN]
            else:
                processed_df['contact'] = "N/A"
            
            logger.info(f"Processed {len(df)} records from Acumeni API")
            logger.info(f"Columns in processed data: {processed_df.columns.tolist()}")
            
            if not processed_df.empty:
                logger.info(f"Sample processed data (first row): {processed_df.iloc[0].to_dict()}")
                
                non_empty_comments = processed_df['comments'].notna() & (processed_df['comments'].astype(str) != '')
                logger.info(f"Number of records with non-empty comments: {non_empty_comments.sum()}")
                
                if non_empty_comments.sum() > 0:
                    sample_comment = processed_df[non_empty_comments].iloc[0]['comments']
                    logger.info(f"Sample comment: {sample_comment}")
            
            return processed_df
            
        except Exception as e:
            logger.error(f"Error processing data: {e}")
            logger.error(traceback.format_exc())
            return pd.DataFrame()
    
    def reprocess_raw_data(self):
        """Reprocess existing raw data if needed"""
        try:
            if os.path.exists(RAW_DATA_FILE) and (not os.path.exists(PROCESSED_DATA_FILE) or input("Reprocess raw data? (y/n): ").lower() == 'y'):
                raw_df = pd.read_csv(RAW_DATA_FILE)
                logger.info(f"Reprocessing {len(raw_df)} records from raw data file")
                processed_df = self._process_data(raw_df)
                processed_df.to_csv(PROCESSED_DATA_FILE, index=False)
                logger.info(f"Created new processed data file with {len(processed_df)} records")
                return True
            return False
        except Exception as e:
            logger.error(f"Error reprocessing raw data: {e}")
            logger.error(traceback.format_exc())
            return False

class DataAnalyzer:
    def __init__(self):
        self.data = None
        self.sentiment_scores = self._load_sentiment_scores()
        self.comment_themes = self._load_comment_themes()
        self.starred_comments = self._load_starred_comments()

        # Get industry themes from environment variables
        self.industry_themes = ENV.get("INDUSTRY_THEMES", get_default_industry_themes("Healthcare"))

        # Extract theme names for available themes
        self.available_themes = list(self.industry_themes.keys())

        self.max_records_for_visualization = 1000
        self.data_retention_days = 90
    
    def _load_sentiment_scores(self):
        """Load previously analyzed sentiment scores"""
        try:
            if os.path.exists(SENTIMENT_SCORES_FILE):
                with open(SENTIMENT_SCORES_FILE, 'r') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Error loading sentiment scores: {e}")
            logger.error(traceback.format_exc())
            return {}
    
    def _save_sentiment_scores(self):
        """Save sentiment scores to file"""
        try:
            with open(SENTIMENT_SCORES_FILE, 'w') as f:
                json.dump(self.sentiment_scores, f)
        except Exception as e:
            logger.error(f"Error saving sentiment scores: {e}")
            logger.error(traceback.format_exc())
    
    def _load_comment_themes(self):
        """Load previously identified comment themes"""
        try:
            if os.path.exists(THEMES_FILE):
                with open(THEMES_FILE, 'r') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Error loading comment themes: {e}")
            logger.error(traceback.format_exc())
            return {}
    
    def _save_comment_themes(self):
        """Save comment themes to file"""
        try:
            with open(THEMES_FILE, 'w') as f:
                json.dump(self.comment_themes, f)
        except Exception as e:
            logger.error(f"Error saving comment themes: {e}")
            logger.error(traceback.format_exc())
    
    def _load_starred_comments(self):
        """Load starred comments from file"""
        try:
            if os.path.exists(STARRED_COMMENTS_FILE):
                with open(STARRED_COMMENTS_FILE, 'r') as f:
                    return json.load(f)
            return []
        except Exception as e:
            logger.error(f"Error loading starred comments: {e}")
            logger.error(traceback.format_exc())
            return []
    
    def _save_starred_comments(self):
        """Save starred comments to file"""
        try:
            with open(STARRED_COMMENTS_FILE, 'w') as f:
                json.dump(self.starred_comments, f)
            logger.info(f"Saved {len(self.starred_comments)} starred comments")
        except Exception as e:
            logger.error(f"Error saving starred comments: {e}")
            logger.error(traceback.format_exc())
    
    def star_comment(self, session_id):
        """Add a comment to starred comments"""
        try:
            if session_id not in self.starred_comments:
                self.starred_comments.append(session_id)
                self._save_starred_comments()
                logger.info(f"Added comment {session_id} to starred comments")
                return True
            return False
        except Exception as e:
            logger.error(f"Error starring comment: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def unstar_comment(self, session_id):
        """Remove a comment from starred comments"""
        try:
            if session_id in self.starred_comments:
                self.starred_comments.remove(session_id)
                self._save_starred_comments()
                logger.info(f"Removed comment {session_id} from starred comments")
                return True
            return False
        except Exception as e:
            logger.error(f"Error unstarring comment: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def load_data(self):
        """Load processed survey data from file"""
        try:
            if os.path.exists(PROCESSED_DATA_FILE):
                self.data = pd.read_csv(PROCESSED_DATA_FILE)
                logger.info(f"Loaded {len(self.data)} records from processed data file")
                
                logger.info(f"Columns in loaded data: {self.data.columns.tolist()}")
                if not self.data.empty:
                    non_empty_comments = self.data['comments'].notna() & (self.data['comments'].astype(str) != '')
                    logger.info(f"Number of records with non-empty comments: {non_empty_comments.sum()}")
                
                if len(self.data) > self.max_records_for_visualization:
                    logger.info(f"Limiting visualization data to {self.max_records_for_visualization} most recent records")
                    self.data = self.data.sort_values('date', ascending=False).head(self.max_records_for_visualization)
                
                try:
                    self.data['date'] = pd.to_datetime(self.data['date'], errors='coerce')
                except Exception as e:
                    logger.error(f"Error converting dates to datetime: {e}")
                    logger.error(traceback.format_exc())
                
                return True
            else:
                logger.warning("No processed data file found")
                return False
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def analyze_sentiment(self):
        """Analyze sentiment of comments with improved batch processing"""
        if self.data is None:
            logger.warning("No data loaded for sentiment analysis")
            return
        
        try:
            comments_to_analyze = []
            session_ids = []
            
            for index, row in self.data.iterrows():
                session_id = str(row.get('session_id', str(index)))
                comment = row.get('comments', '')
                
                if session_id not in self.sentiment_scores and isinstance(comment, str) and comment and comment.strip():
                    comments_to_analyze.append(comment)
                    session_ids.append(session_id)
            
            logger.info(f"Analyzing sentiment for {len(comments_to_analyze)} new comments")
            
            if comments_to_analyze:
                batch_size = 50
                results = analyze_sentiment(comments_to_analyze, batch_size)
                
                for i, result in enumerate(results):
                    session_id = session_ids[i]
                    self.sentiment_scores[session_id] = {
                        'label': result['label'],
                        'score': float(result['score'])
                    }
            
            self._save_sentiment_scores()
            logger.info(f"Completed sentiment analysis, total records: {len(self.sentiment_scores)}")
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
            logger.error(traceback.format_exc())
    
    def identify_themes(self):
        """Identify common themes in comments using industry-specific themes"""
        if self.data is None:
            logger.warning("No data loaded for theme identification")
            return

        try:
            # Prepare themes and patterns from the industry-specific themes
            theme_patterns = {}
            for theme, keywords in self.industry_themes.items():
                theme_patterns[theme] = [keyword.lower() for keyword in keywords]

            logger.info(f"Using {len(theme_patterns)} theme categories for identification")

            batch_size = 100
            new_themes = {}

            unanalyzed_comments = []
            unanalyzed_ids = []

            for index, row in self.data.iterrows():
                session_id = str(row.get('session_id', str(index)))
                comment = row.get('comments', '')

                if session_id not in self.comment_themes and isinstance(comment, str) and comment and comment.strip():
                    unanalyzed_comments.append(comment.lower())
                    unanalyzed_ids.append(session_id)

            logger.info(f"Found {len(unanalyzed_comments)} unanalyzed comments for theme identification")

            for i in range(0, len(unanalyzed_comments), batch_size):
                batch_comments = unanalyzed_comments[i:i+batch_size]
                batch_ids = unanalyzed_ids[i:i+batch_size]

                for j, comment in enumerate(batch_comments):
                    session_id = batch_ids[j]
                    detected_themes = []

                    for theme, patterns in theme_patterns.items():
                        for pattern in patterns:
                            if pattern in comment:
                                detected_themes.append(theme)
                                break

                    new_themes[session_id] = detected_themes

            self.comment_themes.update(new_themes)
            self._save_comment_themes()

            theme_distribution = {}
            for themes_list in new_themes.values():
                for theme in themes_list:
                    theme_distribution[theme] = theme_distribution.get(theme, 0) + 1

            top_themes = sorted(theme_distribution.items(), key=lambda x: x[1], reverse=True)[:5]
            theme_summary = ", ".join([f"{t[0]}: {t[1]}" for t in top_themes])

            logger.info(f"Completed theme identification, processed {len(new_themes)} new comments")
            if top_themes:
                logger.info(f"Top themes identified: {theme_summary}")

        except Exception as e:
            logger.error(f"Error identifying themes: {e}")
            logger.error(traceback.format_exc())
    
    def get_dashboard_data(self):
        """Prepare data for dashboard with improved efficiency"""
        if self.data is None or len(self.data) == 0:
            return None

        try:
            dashboard_data = {}

            # Add active industry to dashboard data for UI reference
            dashboard_data['active_industry'] = ENV.get("ACTIVE_INDUSTRY", "Healthcare")

            if 'csat' in self.data.columns:
                csat_values = pd.to_numeric(self.data['csat'], errors='coerce')
                csat_values = csat_values.dropna()

                if len(csat_values) > 0:
                    dashboard_data['csat_avg'] = float(csat_values.mean())
                else:
                    dashboard_data['csat_avg'] = 0.0

                try:
                    positive = (csat_values >= 7).sum()
                    neutral = ((csat_values >= 5) & (csat_values < 7)).sum()
                    negative = (csat_values < 5).sum()

                    dashboard_data['csat_breakdown'] = {
                        'positive': int(positive),
                        'neutral': int(neutral),
                        'negative': int(negative)
                    }
                except Exception as e:
                    logger.error(f"Error creating CSAT breakdown: {e}")
                    logger.error(traceback.format_exc())
                    dashboard_data['csat_breakdown'] = {
                        'positive': 65,
                        'neutral': 23,
                        'negative': 12
                    }

            if 'nps' in self.data.columns:
                try:
                    nps_values = pd.to_numeric(self.data['nps'], errors='coerce').dropna()

                    if len(nps_values) > 0:
                        promoters = (nps_values >= 9).sum()
                        detractors = (nps_values <= 6).sum()
                        passives = len(nps_values) - promoters - detractors
                        total = len(nps_values)

                        if total > 0:
                            nps_score = (promoters / total * 100) - (detractors / total * 100)
                        else:
                            nps_score = 0
                    else:
                        promoters, passives, detractors = 7, 2, 1
                        nps_score = 50.0

                    dashboard_data['nps_score'] = float(nps_score)
                    dashboard_data['nps_breakdown'] = {
                        'promoters': int(promoters),
                        'passives': int(passives),
                        'detractors': int(detractors)
                    }
                except Exception as e:
                    logger.error(f"Error processing NPS stats: {e}")
                    logger.error(traceback.format_exc())
                    dashboard_data['nps_score'] = 50.0
                    dashboard_data['nps_breakdown'] = {'promoters': 7, 'passives': 2, 'detractors': 1}

            try:
                sentiment_counts = {'POSITIVE': 0, 'NEUTRAL': 0, 'NEGATIVE': 0}

                batch_size = 500
                session_ids = self.data['session_id'].astype(str).tolist()

                for i in range(0, len(session_ids), batch_size):
                    batch_ids = session_ids[i:i+batch_size]

                    for sid in batch_ids:
                        label = self.sentiment_scores.get(sid, {}).get('label', 'NEUTRAL')
                        sentiment_counts[label] = sentiment_counts.get(label, 0) + 1

                dashboard_data['sentiment_counts'] = pd.DataFrame({
                    'index': list(sentiment_counts.keys()),
                    'sentiment': list(sentiment_counts.values())
                })
            except Exception as e:
                logger.error(f"Error processing sentiment stats: {e}")
                logger.error(traceback.format_exc())
                dashboard_data['sentiment_counts'] = pd.DataFrame({
                    'index': ['POSITIVE', 'NEUTRAL', 'NEGATIVE'],
                    'sentiment': [6, 3, 1]
                })

            try:
                # Process themes using the industry-specific theme categories
                theme_counts = {}

                # Count theme occurrences in all comments
                for themes in self.comment_themes.values():
                    for theme in themes:
                        theme_counts[theme] = theme_counts.get(theme, 0) + 1

                # Check if we have theme data
                if theme_counts:
                    # Sort themes by frequency
                    sorted_themes = sorted(theme_counts.items(), key=lambda x: x[1], reverse=True)

                    # Get theme names from industry themes (for proper ordering & display)
                    industry_theme_names = list(self.industry_themes.keys())

                    # Reorder themes to match industry theme order when possible
                    ordered_themes = []
                    for theme_name in industry_theme_names:
                        if theme_name in dict(sorted_themes):
                            ordered_themes.append((theme_name, theme_counts[theme_name]))

                    # Add any missing themes that weren't in industry_theme_names
                    for theme, count in sorted_themes:
                        if theme not in industry_theme_names:
                            ordered_themes.append((theme, count))

                    # Take top 10 themes
                    top_themes = ordered_themes[:10]

                    dashboard_data['theme_counts'] = pd.DataFrame({
                        'index': [t[0] for t in top_themes],
                        'count': [t[1] for t in top_themes]
                    })
                else:
                    # Create sample data based on industry themes
                    industry = ENV.get("ACTIVE_INDUSTRY", "Healthcare")
                    default_themes = list(self.industry_themes.keys())[:3]

                    dashboard_data['theme_counts'] = pd.DataFrame({
                        'index': default_themes,
                        'count': [5, 3, 2]
                    })

                # Include full theme list for reference
                dashboard_data['available_themes'] = self.available_themes

            except Exception as e:
                logger.error(f"Error processing theme stats: {e}")
                logger.error(traceback.format_exc())
                dashboard_data['theme_counts'] = pd.DataFrame({
                    'index': list(self.industry_themes.keys())[:3],
                    'count': [5, 3, 2]
                })
                dashboard_data['available_themes'] = list(self.industry_themes.keys())

            return dashboard_data

        except Exception as e:
            logger.error(f"Error preparing dashboard data: {e}")
            logger.error(traceback.format_exc())
            return None
    
    def get_theme_options(self):
        """Get all unique themes for the dropdown using industry-specific themes"""
        try:
            # Start with industry-specific theme names from environment variables
            themes = set(self.industry_themes.keys())

            # Add any additional themes found in comment data
            for theme_list in self.comment_themes.values():
                for theme in theme_list:
                    themes.add(theme)

            # Sort themes, but prioritize industry themes
            industry_theme_names = list(self.industry_themes.keys())
            sorted_industry_themes = []
            other_themes = []

            for theme in themes:
                if theme in industry_theme_names:
                    sorted_industry_themes.append(theme)
                else:
                    other_themes.append(theme)

            # Sort each group alphabetically
            sorted_industry_themes.sort()
            other_themes.sort()

            # Combine industry themes first, then other themes
            all_sorted_themes = sorted_industry_themes + other_themes

            # Create dropdown options
            options = [{"label": "All Themes", "value": "all"}]
            options.extend([{"label": theme, "value": theme} for theme in all_sorted_themes])

            logger.info(f"Generated {len(options)} theme options for dropdown")
            return options
        except Exception as e:
            logger.error(f"Error getting theme options: {e}")
            logger.error(traceback.format_exc())

            # Use industry themes as fallback
            industry = ENV.get("ACTIVE_INDUSTRY", "Healthcare")
            default_themes = list(self.industry_themes.keys())
            fallback_options = [{"label": "All Themes", "value": "all"}]
            fallback_options.extend([{"label": theme, "value": theme} for theme in default_themes])

            return fallback_options
    
    def get_comments_by_date_range(self, start_date=None, end_date=None, theme_filter="all", show_starred_only=False, max_comments=100):
        """Get comments filtered by date range, theme, and star status"""
        try:
            if self.data is None or len(self.data) == 0:
                logger.warning("No data available for comment filtering")
                return []
            
            filtered_data = self.data.copy()
            
            if 'date' in filtered_data.columns:
                logger.info("Converting date column to datetime format")
                filtered_data['date'] = pd.to_datetime(filtered_data['date'], errors='coerce')
            else:
                logger.warning("Date column not found in data")
                return []
            
            if start_date and end_date:
                try:
                    logger.info(f"Applying explicit date filter: {start_date} to {end_date}")
                    if isinstance(start_date, str):
                        start_date = pd.to_datetime(start_date)
                    if isinstance(end_date, str):
                        end_date = pd.to_datetime(end_date)
                    
                    end_date = end_date + pd.Timedelta(days=1)
                    
                    date_mask = (filtered_data['date'] >= start_date) & (filtered_data['date'] < end_date)
                    filtered_data = filtered_data[date_mask]
                    logger.info(f"Filtered data by date range: {start_date} to {end_date}, {len(filtered_data)} records remaining")
                except Exception as e:
                    logger.error(f"Error filtering by date range: {e}")
                    logger.error(traceback.format_exc())
                    logger.info("Date filter error - showing all dates")
            else:
                logger.info("No date range specified, showing all dates")
            
            has_comments = filtered_data['comments'].notna() & (filtered_data['comments'].astype(str) != '') & (filtered_data['comments'].astype(str).str.lower() != 'nan')
            filtered_data = filtered_data[has_comments]
            logger.info(f"After filtering for non-empty comments: {len(filtered_data)} records remaining")
            
            processed_comments = []
            
            starred_session_ids = set(self.starred_comments)
            logger.info(f"Number of starred comments available: {len(starred_session_ids)}")
            
            if show_starred_only:
                logger.info("Filtering for starred comments only")
            
            for index, row in filtered_data.iterrows():
                session_id = str(row.get('session_id', ''))
                
                if show_starred_only and session_id not in starred_session_ids:
                    continue
                
                themes = self.comment_themes.get(session_id, [])
                
                if theme_filter != "all" and theme_filter not in themes:
                    continue
                
                sentiment = self.sentiment_scores.get(session_id, {}).get('label', 'NEUTRAL')
                
                raw_date = row.get('date', '')
                if isinstance(raw_date, pd.Timestamp):
                    display_date = raw_date.strftime('%Y-%m-%d')
                elif pd.isna(raw_date):
                    display_date = ''
                else:
                    display_date = str(raw_date)
                
                themes_str = ", ".join(themes) if themes else "None"
                processed_comments.append({
                    'session_id': session_id,
                    'date': display_date,
                    'csat': str(row.get('csat', '')),
                    'nps': str(row.get('nps', '')),
                    'comments': str(row.get('comments', '')),
                    'contact': str(row.get('contact', '')),
                    'sentiment': sentiment,
                    'themes': themes_str,
                    'starred': session_id in starred_session_ids
                })
            
            processed_comments = sorted(processed_comments, key=lambda x: x['date'], reverse=True)
            processed_comments = sorted(processed_comments, key=lambda x: x['date'] if x['date'] else '', reverse=True)
            
            if max_comments and len(processed_comments) > max_comments:
                processed_comments = processed_comments[:max_comments]
                logger.info(f"Limited comments to the top {max_comments}")
            
            logger.info(f"Returning {len(processed_comments)} processed comments")
            return processed_comments
        except Exception as e:
            logger.error(f"Error getting filtered comments: {e}")
            logger.error(traceback.format_exc())
            return []
    
    def generate_action_recommendations(self, num_comments=250):
        if not _OPENAI_AVAILABLE:
            return '🔒 OpenAI SDK not available. Install openai to enable recommendations.'
        if not hasattr(openai, 'api_key') or not openai.api_key:
            return '🔑 OpenAI API key not configured. Add OPENAI_API_KEY to your .env file.'
        if self.data is None or 'comments' not in self.data:
            return 'No data available for recommendations.'

        # Get the most recent comments, limited to the specified number
        comments_data = self.data['comments'].dropna().astype(str).tolist()
        if len(comments_data) == 0:
            return 'No comments available for analysis.'

        recent = comments_data[-min(num_comments, len(comments_data)):]
        logger.info(f"Analyzing {len(recent)} comments for recommendations")

        prompt = (
            'You are an expert in customer feedback analysis. Analyze the following comments, '
            'identify trends, interpret sentiment, and recommend actions. Format your response with '
            '"Strengths:" and "Areas for Improvement:" sections:\n\n' + '\n'.join(recent)
        )

        try:
            logger.info("Sending request to OpenAI API")
            try:
                # Try new OpenAI client API (v1.0.0+)
                response = openai.chat.completions.create(
                    model='gpt-4',
                    messages=[
                        {'role': 'system', 'content': 'You are an expert in customer feedback analysis.'},
                        {'role': 'user', 'content': prompt}
                    ],
                    max_tokens=600,
                    temperature=0.6
                )
                result = response.choices[0].message.content.strip()
            except AttributeError:
                # Fall back to legacy OpenAI API
                logger.info("Using legacy OpenAI API")
                response = openai.ChatCompletion.create(
                    model='gpt-4',
                    messages=[
                        {'role': 'system', 'content': 'You are an expert in customer feedback analysis.'},
                        {'role': 'user', 'content': prompt}
                    ],
                    max_tokens=600,
                    temperature=0.6
                )
                result = response.choices[0].message.content.strip()

            logger.info("Successfully received recommendations from OpenAI")
            return result
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            logger.error(traceback.format_exc())
            return f'Error generating recommendations: {str(e)}'
    def cleanup_old_data(self):
        """Remove data older than the retention period"""
        try:
            if self.data is None or 'date' not in self.data.columns:
                return
            
            dates = pd.to_datetime(self.data['date'], errors='coerce')
            cutoff_date = pd.Timestamp.now() - pd.Timedelta(days=self.data_retention_days)
            
            keep_mask = dates >= cutoff_date
            
            if not keep_mask.all():
                records_to_remove = (~keep_mask).sum()
                logger.info(f"Cleaning up {records_to_remove} records older than {self.data_retention_days} days")
                
                self.data = self.data[keep_mask].reset_index(drop=True)
                
                self.data.to_csv(PROCESSED_DATA_FILE, index=False)
                
                removed_session_ids = set()
                for session_id in list(self.sentiment_scores.keys()):
                    if session_id not in set(self.data['session_id'].astype(str)):
                        removed_session_ids.add(session_id)
                        del self.sentiment_scores[session_id]
                
                for session_id in list(self.comment_themes.keys()):
                    if session_id not in set(self.data['session_id'].astype(str)):
                        removed_session_ids.add(session_id)
                        del self.comment_themes[session_id]
                
                self._save_sentiment_scores()
                self._save_comment_themes()
                
                logger.info(f"Removed {len(removed_session_ids)} session IDs from sentiment and theme storage")
        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")
            logger.error(traceback.format_exc())

class DashboardApp:
    def __init__(self, api, analyzer):
        self.api = api
        self.analyzer = analyzer
        self.app = Dash(
            __name__, 
            external_stylesheets=[dbc.themes.BOOTSTRAP],
            title=DASHBOARD_TITLE,
            meta_tags=[
                {"name": "viewport", "content": "width=device-width, initial-scale=1"}
            ]
        )
        self.setup_layout()
        self.setup_callbacks()
        self.data_loaded = False
        
    def refresh_data(self):
        """Refresh data from API and analyze with timeout protection"""
        try:
            logger.info("Refreshing data from API")
            global DASHBOARD_TITLE, DASHBOARD_SUBTITLE, CSAT_QUESTION, NPS_QUESTION, VERBATIM_QUESTION, PHONE_COLUMN
            env_vars = load_environment_variables()
            DASHBOARD_TITLE = env_vars["DASHBOARD_TITLE"]
            DASHBOARD_SUBTITLE = env_vars["DASHBOARD_SUBTITLE"]
            CSAT_QUESTION = env_vars["CSAT_QUESTION"]
            NPS_QUESTION = env_vars["NPS_QUESTION"]
            VERBATIM_QUESTION = env_vars["VERBATIM_QUESTION"]
            PHONE_COLUMN = env_vars["PHONE_COLUMN"]
            
            success = False
            
            try:
                success = self.api.get_survey_data(SURVEY_CODE)
            except Exception as e:
                logger.error(f"Error in API call during refresh: {e}")
                logger.error(traceback.format_exc())
            
            if success or os.path.exists(PROCESSED_DATA_FILE):
                self.analyzer.load_data()
                self.analyzer.analyze_sentiment()
                self.analyzer.identify_themes()
                
                global LAST_UPDATE_TIME
                LAST_UPDATE_TIME = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                
                return True
            return False
        except Exception as e:
            logger.error(f"Error in refresh_data: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def create_sample_data(self):
        """Create sample data if no real data exists for testing purposes"""
        try:
            logger.info("Creating sample data...")
            today = pd.Timestamp.now()
            sample_data = []
            
            for days_ago in range(60):
                date = today - pd.Timedelta(days=days_ago)
                for i in range(np.random.randint(1, 4)):
                    csat = np.random.randint(1, 11)
                    nps = np.random.randint(0, 11)
                    
                    comment_options = [
                        "The staff was very friendly and helpful.",
                        "I had to wait too long to see the doctor.",
                        "The facility was clean and well-maintained.",
                        "The doctor didn't explain my treatment options clearly.",
                        "Overall a positive experience with the hospital.",
                        "The waiting room was too crowded and dirty.",
                        "Nurses were kind but seemed understaffed.",
                        "I appreciated how thoroughly the doctor explained everything.",
                        "The billing process was confusing and frustrating.",
                        "The equipment seemed outdated."
                    ]
                    comment = np.random.choice(comment_options)
                    
                    contact_options = [
                        "555-123-4567",
                        "<EMAIL>",
                        "555-987-6543",
                        "<EMAIL>",
                        "555-555-5555",
                        "<EMAIL>"
                    ]
                    contact = np.random.choice(contact_options)
                    
                    session_id = f"sample_{days_ago}_{i}"
                    
                    sample_data.append({
                        'session_id': session_id,
                        'date': date.strftime('%Y-%m-%d'),
                        'csat': csat,
                        'nps': nps,
                        'comments': comment,
                        'contact': contact
                    })
            
            df = pd.DataFrame(sample_data)
            df.to_csv(PROCESSED_DATA_FILE, index=False)
            logger.info(f"Created sample data with {len(df)} records")
            return True
        except Exception as e:
            logger.error(f"Error creating sample data: {e}")
            logger.error(traceback.format_exc())
            return False
        
    def setup_layout(self):
        """Set up the dashboard layout with new features"""
        try:
            logger.info("Setting up dashboard layout...")
            self.app.layout = dbc.Container([
                dbc.Row([
                    dbc.Col([
                        html.H1(id="dashboard-title", children=DASHBOARD_TITLE, className="text-center my-4"),
                        html.P(id="dashboard-subtitle", children=DASHBOARD_SUBTITLE, className="text-center text-muted mb-5")
                    ])
                ]),
                
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardBody([
                                html.H4("Last Data Update", className="card-title text-center"),
                                html.P(id="last-update-time", className="card-text text-center fs-4"),
                            ])
                        ], className="h-100 shadow")
                    ], width=4),
                    
                    dbc.Col([
                        dbc.Card([
                            dbc.CardBody([
                                html.H4("CSAT Score", className="card-title text-center"),
                                html.P(id="avg-csat", className="card-text text-center fs-1 fw-bold")
                            ])
                        ], className="h-100 shadow")
                    ], width=4),
                    
                    dbc.Col([
                        dbc.Card([
                            dbc.CardBody([
                                html.H4("Net Promoter Score", className="card-title text-center"),
                                html.P(id="nps-score", className="card-text text-center fs-1 fw-bold")
                            ])
                        ], className="h-100 shadow")
                    ], width=4)
                ], className="mb-4"),
                
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader(html.H5("CSAT Breakdown")),
                            dbc.CardBody([
                                dcc.Graph(id="csat-breakdown")
                            ])
                        ], className="shadow")
                    ], width=6),
                    
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader(html.H5("NPS Breakdown")),
                            dbc.CardBody([
                                dcc.Graph(id="nps-breakdown")
                            ])
                        ], className="shadow")
                    ], width=6)
                ], className="mb-4"),
                
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader(html.H5("Sentiment Analysis")),
                            dbc.CardBody([
                                dcc.Graph(id="sentiment-analysis")
                            ])
                        ], className="shadow")
                    ], width=6),
                    
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader(html.H5("Key Themes in Comments")),
                            dbc.CardBody([
                                dcc.Graph(id="comment-themes")
                            ])
                        ], className="shadow")
                    ], width=6)
                ], className="mb-4"),
                
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader([
                                html.H5("Feedback Comments", className="d-inline mb-3"),
                                dbc.Row([
                                    dbc.Col([
                                        html.Label("Date Range:", className="mt-2"),
                                        dbc.Row([
                                            dbc.Col([
                                                dcc.DatePickerRange(
                                                    id="date-range-picker",
                                                    start_date_placeholder_text="Start Date",
                                                    end_date_placeholder_text="End Date",
                                                    calendar_orientation="horizontal",
                                                    clearable=True,
                                                    with_portal=True,
                                                    className="mt-1"
                                                ),
                                            ]),
                                        ]),
                                    ], width=6),
                                    
                                    dbc.Col([
                                        html.Label("Filter by Theme:", className="mt-2"),
                                        dcc.Dropdown(
                                            id="theme-filter-dropdown",
                                            options=self.analyzer.get_theme_options(),
                                            value="all",
                                            clearable=False,
                                            className="mt-1"
                                        ),
                                    ], width=3),
                                    
                                    dbc.Col([
                                        html.Label("# Comments:", className="mt-2 d-block text-end"),
                                        dbc.Input(
                                            id="comments-count-input",
                                            type="number",
                                            min=5,
                                            max=1000,
                                            value=100,
                                            className="mt-1 float-end",
                                            style={"width": "80px"}
                                        ),
                                    ], width=3, className="text-end"),
                                ], className="mt-2"),
                                
                                dbc.Row([
                                    dbc.Col([
                                        dbc.Checkbox(
                                            id="starred-only-checkbox",
                                            label="Show Starred Comments Only",
                                            value=False,
                                            className="mt-2"
                                        ),
                                    ], width=6),
                                    dbc.Col([
                                        dbc.Button(
                                            "Apply Filters", 
                                            id="apply-filters-button", 
                                            color="primary", 
                                            size="sm", 
                                            className="mt-2 float-end"
                                        ),
                                    ], width=6, className="text-end"),
                                ], className="mt-2"),
                            ]),
                            dbc.CardBody([
                                dash_table.DataTable(
                                    id="comments-table",
                                    columns=[
                                        {"name": "Date", "id": "date"},
                                        {"name": "CSAT", "id": "csat"},
                                        {"name": "NPS", "id": "nps"},
                                        {"name": "Comment", "id": "comments"},
                                        {"name": "Sentiment", "id": "sentiment"},
                                        {"name": "Themes", "id": "themes"},
                                        {"name": "Actions", "id": "actions", "presentation": "markdown"}
                                    ],
                                    page_size=20,
                                    page_current=0,
                                    page_action='native',
                                    style_header={
                                        'backgroundColor': 'rgb(230, 230, 230)',
                                        'fontWeight': 'bold'
                                    },
                                    style_data={
                                        'whiteSpace': 'normal',
                                        'height': 'auto',
                                    },
                                    style_table={'overflowX': 'auto'},
                                    style_cell={
                                        'textAlign': 'left',
                                        'padding': '15px'
                                    },
                                    style_cell_conditional=[
                                        {'if': {'column_id': 'comments'},
                                         'width': '40%'},
                                        {'if': {'column_id': 'actions'},
                                         'width': '100px'},
                                    ],
                                    markdown_options={"html": True}
                                ),
                                dcc.Store(id="selected-comment-id"),
                                
                                html.Div(id="action-type-store", style={"display": "none"}),
                                
                                html.Script('''
                                document.addEventListener('click', function(event) {
                                    if (event.target && event.target.classList.contains('contact-info-btn')) { || event.target.classList.contains('share-btn')
                                        event.stopPropagation();
                                        event.preventDefault();
                                        return false;
                                    }
                                }, true);
                                ''')
                            ])
                        ], className="shadow")
                    ])
                ]),
                
                dbc.Modal([
                    dbc.ModalHeader(dbc.ModalTitle("Comment Action")),
                    dbc.ModalBody(id="comment-action-modal-body"),
                    dbc.ModalFooter([
                        dbc.Button("Cancel", id="comment-action-cancel", className="me-2"),
                        dbc.Button("Confirm", id="comment-action-confirm", color="primary")
                    ])
                ], id="comment-action-modal", is_open=False),
                
                dbc.Row([
                    dbc.Col([
                        html.Footer([
                            html.P(f"© 2025 {DASHBOARD_TITLE}", className="text-center text-muted mt-4")
                        ])
                    ])
                ]),
                
                dcc.Interval(
                    id='interval-component',
                    interval=5*60*1000,
                    n_intervals=0
                )
            ], fluid=True, className="px-4 py-3")
            
            logger.info("Dashboard layout setup complete")
        except Exception as e:
            logger.error(f"Error setting up dashboard layout: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def setup_callbacks(self):
        """Set up dashboard callbacks with new features"""
        try:
            logger.info("Setting up dashboard callbacks...")
            @self.app.callback(
                Output("last-update-time", "children"),
                [Input("interval-component", "n_intervals")],
                prevent_initial_call=True
            )
            def update_last_update_time(n_intervals):
                return LAST_UPDATE_TIME
            @self.app.callback(
                [Output("avg-csat", "children"),
                 Output("nps-score", "children")],
                [Input("last-update-time", "children")]
            )
            def update_scores(last_update):
                try:
                    dashboard_data = self.analyzer.get_dashboard_data()
                    
                    if dashboard_data is None:
                        return "N/A", "N/A"
                    
                    csat_pct = f"{dashboard_data.get('csat_avg', 0) * 10:.1f}%"
                    
                    nps_score = f"{dashboard_data.get('nps_score', 0):.1f}"
                    
                    return csat_pct, nps_score
                except Exception as e:
                    logger.error(f"Error in update_scores callback: {e}")
                    logger.error(traceback.format_exc())
                    return "Error", "Error"
            
            @self.app.callback(
                Output("csat-breakdown", "figure"),
                [Input("last-update-time", "children")]
            )
            def update_csat_breakdown(last_update):
                try:
                    dashboard_data = self.analyzer.get_dashboard_data()
                    
                    if dashboard_data is None:
                        empty_fig = go.Figure()
                        empty_fig.update_layout(title="No data available")
                        return empty_fig
                    
                    csat_breakdown = dashboard_data.get('csat_breakdown', {'positive': 65, 'neutral': 23, 'negative': 12})
                    
                    total_responses = csat_breakdown['positive'] + csat_breakdown['neutral'] + csat_breakdown['negative']
                    
                    if total_responses > 0:
                        positive_pct = round(csat_breakdown['positive'] / total_responses * 100, 1)
                        neutral_pct = round(csat_breakdown['neutral'] / total_responses * 100, 1)
                        negative_pct = round(csat_breakdown['negative'] / total_responses * 100, 1)
                    else:
                        positive_pct, neutral_pct, negative_pct = 65.0, 23.2, 11.8
                    
                    csat_fig = go.Figure()
                    csat_fig.add_trace(go.Bar(
                        x=['Positive (7-10)', 'Neutral (5-6)', 'Negative (0-4)'],
                        y=[csat_breakdown['positive'], csat_breakdown['neutral'], csat_breakdown['negative']],
                        marker_color=['#4472C4', '#FFD966', '#FF0000'],
                        text=[f"{positive_pct}%", f"{neutral_pct}%", f"{negative_pct}%"],
                        textposition='auto'
                    ))
                    
                    csat_fig.update_layout(
                        title="Customer Satisfaction Levels",
                        plot_bgcolor="white",
                        yaxis_title="Number of Responses"
                    )
                    
                    return csat_fig
                except Exception as e:
                    logger.error(f"Error in update_csat_breakdown callback: {e}")
                    logger.error(traceback.format_exc())
                    empty_fig = go.Figure()
                    empty_fig.update_layout(title="Error loading data")
                    return empty_fig
            
            @self.app.callback(
                Output("nps-breakdown", "figure"),
                [Input("last-update-time", "children")]
            )
            def update_nps_breakdown(last_update):
                try:
                    dashboard_data = self.analyzer.get_dashboard_data()
                    
                    if dashboard_data is None:
                        empty_fig = go.Figure()
                        empty_fig.update_layout(title="No data available")
                        return empty_fig
                    
                    nps_breakdown = dashboard_data.get('nps_breakdown', {'promoters': 0, 'passives': 0, 'detractors': 0})
                    nps_fig = go.Figure()
                    nps_fig.add_trace(go.Bar(
                        x=["Promoters (9-10)", "Passives (7-8)", "Detractors (0-6)"],
                        y=[nps_breakdown['promoters'], nps_breakdown['passives'], nps_breakdown['detractors']],
                        marker_color=["#00B050", "#FFD966", "#FF0000"],
                        text=[nps_breakdown["promoters"], nps_breakdown["passives"], nps_breakdown["detractors"]],
                        textposition="auto"
                    ))
                    nps_fig.update_layout(
                        title="NPS Breakdown",
                        plot_bgcolor="white"
                    )
                    
                    return nps_fig
                except Exception as e:
                    logger.error(f"Error in update_nps_breakdown callback: {e}")
                    logger.error(traceback.format_exc())
                    empty_fig = go.Figure()
                    empty_fig.update_layout(title="Error loading data")
                    return empty_fig
            
            @self.app.callback(
                Output("sentiment-analysis", "figure"),
                [Input("last-update-time", "children")]
            )
            def update_sentiment_analysis(last_update):
                try:
                    dashboard_data = self.analyzer.get_dashboard_data()
                    
                    if dashboard_data is None:
                        empty_fig = go.Figure()
                        empty_fig.update_layout(title="No data available")
                        return empty_fig
                    
                    sentiment_data = dashboard_data.get('sentiment_counts', pd.DataFrame(columns=['index', 'sentiment']))
                    sentiment_fig = px.pie(
                        sentiment_data,
                        names='index',
                        values='sentiment',
                        title="Comment Sentiment",
                        color='index',
                        color_discrete_map={'POSITIVE': '#00B050', 'NEUTRAL': '#FFD966', 'NEGATIVE': '#FF0000'}
                    )
                    
                    return sentiment_fig
                except Exception as e:
                    logger.error(f"Error in update_sentiment_analysis callback: {e}")
                    logger.error(traceback.format_exc())
                    empty_fig = go.Figure()
                    empty_fig.update_layout(title="Error loading data")
                    return empty_fig
            
            @self.app.callback(
                Output("comment-themes", "figure"),
                [Input("last-update-time", "children")]
            )
            def update_comment_themes(last_update):
                try:
                    dashboard_data = self.analyzer.get_dashboard_data()
                    
                    if dashboard_data is None:
                        empty_fig = go.Figure()
                        empty_fig.update_layout(title="No data available")
                        return empty_fig
                    
                    theme_data = dashboard_data.get('theme_counts', pd.DataFrame(columns=['index', 'count']))
                    theme_fig = px.bar(
                        theme_data,
                        x='index',
                        y='count',
                        title="Key Themes in Comments",
                        labels={"index": "Theme", "count": "Mentions"}
                    )
                    theme_fig.update_layout(plot_bgcolor="white")
                    
                    return theme_fig
                except Exception as e:
                    logger.error(f"Error in update_comment_themes callback: {e}")
                    logger.error(traceback.format_exc())
                    empty_fig = go.Figure()
                    empty_fig.update_layout(title="Error loading data")
                    return empty_fig
            
            @self.app.callback(
                [Output("date-range-picker", "start_date"),
                 Output("date-range-picker", "end_date")],
                [Input("last-update-time", "children")],
                [State("date-range-picker", "start_date"),
                 State("date-range-picker", "end_date")]
            )
            def initialize_date_range(last_update, current_start_date, current_end_date):
                try:
                    logger.info(f"Initializing date range. Current dates: {current_start_date} to {current_end_date}")
                    if current_start_date is None and current_end_date is None:
                        end_date = pd.Timestamp.now()
                        start_date = end_date - pd.Timedelta(days=30)
                        
                        start_str = start_date.strftime('%Y-%m-%d')
                        end_str = end_date.strftime('%Y-%m-%d')
                        
                        logger.info(f"Setting initial date range to: {start_str} to {end_str}")
                        return start_str, end_str
                    
                    logger.info("Date range already set, maintaining current values")
                    return current_start_date, current_end_date
                except Exception as e:
                    logger.error(f"Error in initialize_date_range callback: {e}")
                    logger.error(traceback.format_exc())
                    end_date = pd.Timestamp.now()
                    start_date = end_date - pd.Timedelta(days=30)
                    return start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')
            
            @self.app.callback(
                [Output("comments-table", "data"),
                 Output("theme-filter-dropdown", "options")],
                [Input("apply-filters-button", "n_clicks"),
                 Input("last-update-time", "children")],
                [State("date-range-picker", "start_date"),
                 State("date-range-picker", "end_date"),
                 State("theme-filter-dropdown", "value"),
                 State("comments-count-input", "value"),
                 State("starred-only-checkbox", "value")]
            )
            def update_comments_table(n_clicks, last_update, start_date, end_date, 
                                    selected_theme, comments_count, starred_only):
                try:
                    logger.info(f"Updating comments table with filters - Date range: {start_date} to {end_date}, Theme: {selected_theme}, Count: {comments_count}, Starred only: {starred_only}")
                    
                    if comments_count is None or comments_count < 5:
                        comments_count = 5
                    elif comments_count > 1000:
                        comments_count = 1000
                    
                    comments_data = self.analyzer.get_comments_by_date_range(
                        start_date=start_date,
                        end_date=end_date,
                        theme_filter=selected_theme,
                        show_starred_only=starred_only,
                        max_comments=comments_count
                    )
                    
                    for comment in comments_data:
                        sid = comment['session_id']
                        is_starred = comment['starred']
                        contact_info = comment['contact']
                        feedback_date = comment['date']

                        btns = []
                        if is_starred:
                            btns.append(f'<button class="btn btn-sm btn-warning" data-action="unstar" data-id="{sid}">⭐</button>')
                        else:
                            btns.append(f'<button class="btn btn-sm btn-outline-warning" data-action="star" data-id="{sid}">☆</button>')

                        raw_contact = comment.get('contact', '')
                        try:
                            tooltip_contact = str(int(float(raw_contact)))
                            tooltip = f"Contact: {tooltip_contact}\nDate: {feedback_date}"
                        except Exception:
                            tooltip_contact = str(raw_contact)
                            tooltip = f"Contact: {tooltip_contact}\nDate: {feedback_date}"
                        if contact_info and contact_info not in ("nan","None",""):
                            btns.append(
                                f'<button class="btn btn-sm btn-info contact-info-btn" '
                                f'data-id="{sid}" title="{tooltip}" onclick="event.stopPropagation(); return false;">📞</button>'
                            )

                        csat = comment.get('csat', '')
                        nps = comment.get('nps', '')
                        raw_contact = contact_info
                        try:
                            contact = str(int(float(raw_contact)))
                        except Exception:
                            contact = str(raw_contact)
                        date_str = feedback_date
                        text = comment.get('comments', '')
                        title = urllib.parse.quote(f"Feedback from {date_str}")
                        body = urllib.parse.quote(
                            f"CSAT: {csat}\n"
                            f"NPS: {nps}\n"
                            f"Contact: {contact}\n"
                            f"Date: {date_str}\n\n"
                            f"Comment:\n{text}"
                        )
                        share_href = f"mailto:?subject={title}&body={body}"
                        btns.append(
                            f'<a href="{share_href}" target="_blank" class="btn btn-sm btn-outline-primary share-btn" '
                              f'title="Share this comment via email" onclick="event.stopPropagation()">🔗</a>'
                        )

                        comment['actions'] = (
                            '<div style="display:flex; align-items:center; gap:5px;">'
                            + "".join(btns)
                            + '</div>'
                        )
                    
                    theme_options = self.analyzer.get_theme_options()
                    
                    logger.info(f"Returning {len(comments_data)} comments for display in table")
                    return comments_data, theme_options
                except Exception as e:
                    logger.error(f"Error updating comments table: {e}")
                    logger.error(traceback.format_exc())
                    return [], self.analyzer.get_theme_options()
            
            @self.app.callback(
                [Output("comment-action-modal", "is_open"),
                 Output("comment-action-modal-body", "children"),
                 Output("selected-comment-id", "data")],
                [Input("comments-table", "active_cell")],
                [State("comments-table", "data")]
            )
            def handle_comment_action(active_cell, table_data):
                try:
                    if active_cell is None:
                        return False, "", None
                    
                    if active_cell['column_id'] != 'actions':
                        return False, "", None
                    
                    row_id = active_cell['row']
                    if row_id >= len(table_data):
                        return False, "", None
                    
                    row_data = table_data[row_id]
                    session_id = row_data['session_id']
                    
                    ctx = callback_context
                    if not ctx.triggered:
                        return False, "", None
                    
                    is_starred = session_id in self.analyzer.starred_comments
                    
                    if is_starred:
                        action = "unstar"
                        modal_content = f"Remove this comment from your starred comments list?"
                    else:
                        action = "star"
                        modal_content = f"Add this comment to your starred comments list?"
                        
                    return True, modal_content, {"session_id": session_id, "action": action}
                except Exception as e:
                    logger.error(f"Error handling comment action: {e}")
                    logger.error(traceback.format_exc())
                    return False, "Error processing action", None
            
            @self.app.callback(
                Output("apply-filters-button", "n_clicks", allow_duplicate=True),
                [Input("comment-action-confirm", "n_clicks")],
                [State("selected-comment-id", "data"),
                 State("apply-filters-button", "n_clicks")],
                prevent_initial_call=True
            )
            def confirm_comment_action(confirm_clicks, action_data, current_filters_clicks):
                try:
                    if not confirm_clicks or action_data is None:
                        raise PreventUpdate
                    
                    session_id = action_data.get("session_id")
                    action = action_data.get("action")
                    
                    if not session_id or not action:
                        raise PreventUpdate
                    
                    logger.info(f"Confirming {action} action for comment {session_id}")
                    
                    try:
                        if action == "star":
                            self.analyzer.star_comment(session_id)
                        elif action == "unstar":
                            self.analyzer.unstar_comment(session_id)
                        
                        return (current_filters_clicks or 0) + 1
                    except Exception as e:
                        logger.error(f"Error processing {action} action for comment {session_id}: {e}")
                        logger.error(traceback.format_exc())
                        raise PreventUpdate
                except Exception as e:
                    logger.error(f"Error in confirm_comment_action callback: {e}")
                    logger.error(traceback.format_exc())
                    raise PreventUpdate
            
            @self.app.callback(
                Output("comment-action-modal", "is_open", allow_duplicate=True),
                [Input("comment-action-cancel", "n_clicks"),
                 Input("comment-action-confirm", "n_clicks")],
                prevent_initial_call=True
            )
            def close_comment_action_modal(cancel_clicks, confirm_clicks):
                return False

        except Exception as e:
            logger.error(f"Error setting up dashboard callbacks: {e}")
            logger.error(traceback.format_exc())
            raise


from dash import dcc, html
import dash_bootstrap_components as dbc

_original_setup_layout = DashboardApp.setup_layout
def setup_layout_with_recs(self):
    _original_setup_layout(self)
    self.app.layout.children.insert(
        5,
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader(html.H5('Action Recommendations')),
                    dbc.CardBody([
                        dbc.Button("Get Recommendations", id="get-recs-button", color="secondary", className="mb-3"),
                        dcc.Loading(
                            id="loading-recs",
                            type="circle",
                            children=[
                                dcc.Markdown(
                                    id="action-recommendations",
                                    children="Click 'Get Recommendations' to load suggestions."
                                )
                            ]
                        ),
                    ])
                ], className='shadow mb-4')
            ], width=12)
        ], className='mb-4')
    )
DashboardApp.setup_layout = setup_layout_with_recs

_original_setup_callbacks = DashboardApp.setup_callbacks
def setup_callbacks_with_recs(self):
    _original_setup_callbacks(self)
    @self.app.callback(
        Output('action-recommendations', 'children'),
        [Input('get-recs-button', 'n_clicks')],
        prevent_initial_call=True
    )
    def update_recs(n_clicks):
        if not n_clicks:
            raise PreventUpdate
        raw = self.analyzer.generate_action_recommendations()
        formatted = raw.replace('Strengths:', '### Strengths\n')
        formatted = formatted.replace('Areas for Improvement:', '### Areas for Improvement\n')
        formatted = re.sub(r'^\s*\d+\.\s*(.*)', r'- \1', formatted, flags=re.MULTILINE)
        return formatted
DashboardApp.setup_callbacks = setup_callbacks_with_recs



if __name__ == "__main__":
    try:
        dashboard = None
        api = AcumeniAPI(HOST, USERNAME, PASSWORD)
        analyzer = DataAnalyzer()
        dashboard = DashboardApp(api, analyzer)
        
        dashboard.refresh_data()
        
        scheduler = BackgroundScheduler()
        scheduler.add_job(dashboard.refresh_data, 'interval', minutes=DATA_REFRESH_MINUTES)
        scheduler.start()
        
        dashboard.app.run_server(debug=False)
    except Exception as e:
        logger.error(f"Error starting dashboard: {e}")
        logger.error(traceback.format_exc())

if __name__ == '__main__':
    print('⚙️ Running fallback-enabled dashboard with AI recommendations')