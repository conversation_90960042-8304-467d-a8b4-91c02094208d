{"extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"browser": {"show_home_button": "871ED356A7DB3E48CDC406323EAC35D9CB53AF21A4CCC20BEB9A9D600C7EFE8A"}, "default_search_provider_data": {"template_url_data": "CA95FBCEF0A18C88E43F35B4647E22951005B191226A0C7BE956514A2EA667CD"}, "edge": {"services": {"account_id": "1AC55248A502AF8CB74C735617929E7B62C64E27D4F275824854CD84D4AFD93B", "last_username": "754C85AD918351EFA23518E15CE7C03D222DC3480BF5DD2B7AA6FDC56DFE9F2E"}}, "enterprise_signin": {"policy_recovery_token": "E96111D395B3985442D81814A30B854B9BD5ABA6DEFD61986FFAEC6A21494F77"}, "extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": "DCB3675FD8DC22FFD63988E35A6FD4C22F6EAFCDEEB52DF31A54C87F65BEDC27", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "D94E27ACBFFD0685B55BE575633DE1042BFBED15AEB4B61D52F9D5B6D3B02A22"}, "ui": {"developer_mode": "F8394454A79C2E6D17FE9200CF57B91C9C0E882B642B59054FA889D7E4B965FF"}}, "google": {"services": {"last_signed_in_username": "7695691DCC68C81C39871624EDA71385B2040EFDBA61F6A79F6D2B6147B51BB0"}}, "homepage": "FFAFF9890517037C6BE19A43F621D25D219C508AD7F491523BCC7258362B5758", "homepage_is_newtabpage": "73880E13307977A8FC2E8E678A98974C9891B0CCFBDE0ADC5485D7D8E2A4D6DC", "media": {"cdm": {"origin_data": "3728FABA83706D4017A78089DEAA5C7109966FB81643829BBC63BFE8F10F78BC"}, "storage_id_salt": "0DB06706BF053A1683E143CC45CCBAACD04A5FB998A8AF7F45323E65BBAA3F92"}, "pinned_tabs": "207FDC048D28E6BFB719B52615D68C1673A80A7087C0C7F779E0DEBEB087E371", "prefs": {"preference_reset_time": "87FEC4A56348F82E70FECDF30B619B099FD5237A14A56BD6E3AF984DA62960F6"}, "safebrowsing": {"incidents_sent": "197903FE8F5CD63569031843B889274D7D95C49AC43316B3918B0B5BE76FEC77"}, "search_provider_overrides": "B0FCBC572C7B752E7DB35244ACEE27C2C6B8E81FEF5E1E2758F5344850E1528C", "session": {"restore_on_startup": "710AE047AB8C7AD54E85F2ACF54F51E09559B7A9D49B344C5183829F64ED24D3", "startup_urls": "4DA62AEAD5825D36DE5808020EEA78351F071EE7A6688050D2BBD716AD852537"}}, "super_mac": "5B6ABF9AD0D9BB6691D998583448F643B464DEFF257C2DE313F713DA9A1E05BF"}}